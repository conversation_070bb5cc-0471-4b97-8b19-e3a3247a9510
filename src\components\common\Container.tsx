import React from 'react';
import { Box, Paper, Typography, SxProps, Theme } from '@mui/material';
import { colors } from '../../styles/colors';

interface ContainerProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  sx?: SxProps<Theme>;
  useGradient?: boolean;
}

export const Container = ({
  title,
  subtitle,
  children,
  sx,
  useGradient = false,
}: ContainerProps) => {
  return (
    <Box sx={{ width: '100%', ...sx }}>
      {(title || subtitle) && (
        <Box
          sx={{
            mb: 3,
            p: 3,
            borderRadius: 2,
            background: useGradient ? colors.gradients.primary : 'transparent',
          }}
        >
          {title && (
            <Typography
              variant="h4"
              sx={{
                color: useGradient ? colors.cloud : colors.gray[900],
                mb: subtitle ? 1 : 0,
              }}
            >
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography
              variant="subtitle1"
              sx={{
                color: useGradient ? colors.cloud : colors.gray[700],
                opacity: useGradient ? 0.9 : 1,
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      )}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          borderRadius: 2,
          backgroundColor: colors.cloud,
          boxShadow: colors.shadows.sm,
        }}
      >
        {children}
      </Paper>
    </Box>
  );
};

export default Container; 