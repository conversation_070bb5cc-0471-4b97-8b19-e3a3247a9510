import db from '../database/db';

export interface Treino {
  id?: number;
  cliente_id: number;
  nome: string;
  data_inicio: string;
  data_fim?: string;
  observacoes?: string;
  data_criacao: string;
}

export interface Exercicio {
  id?: number;
  treino_id: number;
  nome: string;
  tipo_treino: string; // A, B, C, D, E, F
  ordem: number;
}

export interface Serie {
  id?: number;
  exercicio_id: number;
  semana: number;
  numero_serie: number;
  repeticoes: number;
  carga: number;
  volume_carga: number;
}

export const TreinoModel = {
  // Métodos para Treino
  criarTreino: (treino: Treino): number => {
    const stmt = db.prepare(`
      INSERT INTO treinos (cliente_id, nome, data_inicio, data_fim, observacoes, data_criacao)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const info = stmt.run(
      treino.cliente_id,
      treino.nome,
      treino.data_inicio,
      treino.data_fim || null,
      treino.observacoes || null,
      new Date().toISOString().split('T')[0]
    );

    return info.lastInsertRowid as number;
  },
  
  obterTodosTreinos: (): Treino[] => {
    const stmt = db.prepare('SELECT * FROM treinos ORDER BY data_inicio DESC');
    return stmt.all();
  },
  
  obterTreinoPorId: (id: number): Treino | undefined => {
    const stmt = db.prepare('SELECT * FROM treinos WHERE id = ?');
    return stmt.get(id);
  },

  obterTreinosPorCliente: (clienteId: number): Treino[] => {
    const stmt = db.prepare('SELECT * FROM treinos WHERE cliente_id = ? ORDER BY data_inicio DESC');
    return stmt.all(clienteId);
  },
  
  atualizarTreino: (id: number, treino: Partial<Treino>): boolean => {
    const campos = Object.keys(treino).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;

    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (treino as any)[campo]);

    const stmt = db.prepare(`UPDATE treinos SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);

    return info.changes > 0;
  },
  
  excluirTreino: (id: number): boolean => {
    const stmt = db.prepare('DELETE FROM treinos WHERE id = ?');
    const info = stmt.run(id);

    return info.changes > 0;
  },
  
  // Métodos para Exercício
  criarExercicio: (exercicio: Exercicio): number => {
    const stmt = db.prepare(`
      INSERT INTO exercicios (treino_id, nome, tipo_treino, ordem)
      VALUES (?, ?, ?, ?)
    `);

    const info = stmt.run(
      exercicio.treino_id,
      exercicio.nome,
      exercicio.tipo_treino,
      exercicio.ordem
    );

    return info.lastInsertRowid as number;
  },
  
  obterExerciciosPorTreino: (treinoId: number): Exercicio[] => {
    const stmt = db.prepare('SELECT * FROM exercicios WHERE treino_id = ? ORDER BY tipo_treino, ordem');
    return stmt.all(treinoId);
  },

  obterExercicioPorId: (id: number): Exercicio | undefined => {
    const stmt = db.prepare('SELECT * FROM exercicios WHERE id = ?');
    return stmt.get(id);
  },
  
  atualizarExercicio: (id: number, exercicio: Partial<Exercicio>): boolean => {
    const campos = Object.keys(exercicio).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;

    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (exercicio as any)[campo]);

    const stmt = db.prepare(`UPDATE exercicios SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);

    return info.changes > 0;
  },

  excluirExercicio: (id: number): boolean => {
    const stmt = db.prepare('DELETE FROM exercicios WHERE id = ?');
    const info = stmt.run(id);

    return info.changes > 0;
  },
  
  // Métodos para Série
  criarSerie: (serie: Serie): number => {
    const stmt = db.prepare(`
      INSERT INTO series (exercicio_id, semana, numero_serie, repeticoes, carga, volume_carga)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const info = stmt.run(
      serie.exercicio_id,
      serie.semana,
      serie.numero_serie,
      serie.repeticoes,
      serie.carga,
      serie.volume_carga
    );

    return info.lastInsertRowid as number;
  },
  
  obterSeriesPorExercicio: (exercicioId: number): Serie[] => {
    const stmt = db.prepare('SELECT * FROM series WHERE exercicio_id = ? ORDER BY semana, numero_serie');
    return stmt.all(exercicioId);
  },

  obterSeriePorId: (id: number): Serie | undefined => {
    const stmt = db.prepare('SELECT * FROM series WHERE id = ?');
    return stmt.get(id);
  },
  
  atualizarSerie: (id: number, serie: Partial<Serie>): boolean => {
    const campos = Object.keys(serie).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;

    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (serie as any)[campo]);

    const stmt = db.prepare(`UPDATE series SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);

    return info.changes > 0;
  },

  excluirSerie: (id: number): boolean => {
    const stmt = db.prepare('DELETE FROM series WHERE id = ?');
    const info = stmt.run(id);

    return info.changes > 0;
  },
  
  // Métodos para obter informações agregadas
  obterVolumeTotal: (exercicioId: number, semana: number): number => {
    const stmt = db.prepare('SELECT SUM(volume_carga) as volume_total FROM series WHERE exercicio_id = ? AND semana = ?');
    const resultado = stmt.get(exercicioId, semana) as { volume_total: number };
    
    return resultado.volume_total || 0;
  },
  
  obterProgressaoVolume: (exercicioId: number): { semana: number, volume_total: number }[] => {
    const stmt = db.prepare(`
      SELECT semana, SUM(volume_carga) as volume_total 
      FROM series 
      WHERE exercicio_id = ? 
      GROUP BY semana 
      ORDER BY semana
    `);
    
    return stmt.all(exercicioId) as { semana: number, volume_total: number }[];
  }
};

export default TreinoModel; 