import db from '../database/db';

// Verificar se estamos no Electron de forma segura para tipagem
const isElectron = typeof window !== 'undefined' && 
                  window.navigator && 
                  /electron/i.test(window.navigator.userAgent);
const isDevelopment = process.env.NODE_ENV === 'development';

export interface Treino {
  id?: number;
  cliente_id: number;
  nome: string;
  data_inicio: string;
  data_fim?: string;
  observacoes?: string;
  data_criacao: string;
}

export interface Exercicio {
  id?: number;
  treino_id: number;
  nome: string;
  tipo_treino: string; // A, B, C, D, E, F
  ordem: number;
}

export interface Serie {
  id?: number;
  exercicio_id: number;
  semana: number;
  numero_serie: number;
  repeticoes: number;
  carga: number;
  volume_carga: number;
}

// Dados mock para desenvolvimento
const mockTreinos: Treino[] = [
  {
    id: 1,
    cliente_id: 1,
    nome: 'Treino de Hipertrofia',
    data_inicio: '2023-05-01',
    data_fim: '2023-07-31',
    observacoes: 'Foco em hipertrofia muscular',
    data_criacao: '2023-05-01'
  },
  {
    id: 2,
    cliente_id: 2,
    nome: 'Treino de Emagrecimento',
    data_inicio: '2023-06-15',
    observacoes: 'Foco em perda de peso',
    data_criacao: '2023-06-15'
  }
];

const mockExercicios: Exercicio[] = [
  {
    id: 1,
    treino_id: 1,
    nome: 'Supino Reto',
    tipo_treino: 'A',
    ordem: 1
  },
  {
    id: 2,
    treino_id: 1,
    nome: 'Puxada Frontal',
    tipo_treino: 'A',
    ordem: 2
  },
  {
    id: 3,
    treino_id: 1,
    nome: 'Leg Press',
    tipo_treino: 'B',
    ordem: 1
  },
  {
    id: 4,
    treino_id: 2,
    nome: 'Esteira',
    tipo_treino: 'A',
    ordem: 1
  }
];

const mockSeries: Serie[] = [
  {
    id: 1,
    exercicio_id: 1,
    semana: 1,
    numero_serie: 1,
    repeticoes: 12,
    carga: 60,
    volume_carga: 720
  },
  {
    id: 2,
    exercicio_id: 1,
    semana: 1,
    numero_serie: 2,
    repeticoes: 10,
    carga: 70,
    volume_carga: 700
  },
  {
    id: 3,
    exercicio_id: 2,
    semana: 1,
    numero_serie: 1,
    repeticoes: 12,
    carga: 50,
    volume_carga: 600
  }
];

export const TreinoModel = {
  // Métodos para Treino
  criarTreino: (treino: Treino): number => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const novoId = mockTreinos.length > 0 ? Math.max(...mockTreinos.map(t => t.id || 0)) + 1 : 1;
      mockTreinos.push({
        ...treino,
        id: novoId,
        data_criacao: new Date().toISOString().split('T')[0]
      });
      return novoId;
    }
    
    const stmt = db.prepare(`
      INSERT INTO treinos (cliente_id, nome, data_inicio, data_fim, observacoes, data_criacao)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    const info = stmt.run(
      treino.cliente_id,
      treino.nome,
      treino.data_inicio,
      treino.data_fim || null,
      treino.observacoes || null,
      new Date().toISOString().split('T')[0]
    );
    
    return info.lastInsertRowid as number;
  },
  
  obterTodosTreinos: (): Treino[] => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return [...mockTreinos];
    }
    
    const stmt = db.prepare('SELECT * FROM treinos ORDER BY data_inicio DESC');
    return stmt.all();
  },
  
  obterTreinoPorId: (id: number): Treino | undefined => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockTreinos.find(t => t.id === id);
    }
    
    const stmt = db.prepare('SELECT * FROM treinos WHERE id = ?');
    return stmt.get(id);
  },
  
  obterTreinosPorCliente: (clienteId: number): Treino[] => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockTreinos.filter(t => t.cliente_id === clienteId);
    }
    
    const stmt = db.prepare('SELECT * FROM treinos WHERE cliente_id = ? ORDER BY data_inicio DESC');
    return stmt.all(clienteId);
  },
  
  atualizarTreino: (id: number, treino: Partial<Treino>): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockTreinos.findIndex(t => t.id === id);
      if (index !== -1) {
        mockTreinos[index] = { ...mockTreinos[index], ...treino, id };
        return true;
      }
      return false;
    }
    
    const campos = Object.keys(treino).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;
    
    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (treino as any)[campo]);
    
    const stmt = db.prepare(`UPDATE treinos SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);
    
    return info.changes > 0;
  },
  
  excluirTreino: (id: number): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockTreinos.findIndex(t => t.id === id);
      if (index !== -1) {
        mockTreinos.splice(index, 1);
        // Remover exercícios e séries relacionados
        const exerciciosIds = mockExercicios.filter(e => e.treino_id === id).map(e => e.id);
        mockExercicios.splice(0, mockExercicios.length, ...mockExercicios.filter(e => e.treino_id !== id));
        mockSeries.splice(0, mockSeries.length, ...mockSeries.filter(s => !exerciciosIds.includes(s.exercicio_id)));
        return true;
      }
      return false;
    }
    
    const stmt = db.prepare('DELETE FROM treinos WHERE id = ?');
    const info = stmt.run(id);
    
    return info.changes > 0;
  },
  
  // Métodos para Exercício
  criarExercicio: (exercicio: Exercicio): number => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const novoId = mockExercicios.length > 0 ? Math.max(...mockExercicios.map(e => e.id || 0)) + 1 : 1;
      mockExercicios.push({
        ...exercicio,
        id: novoId
      });
      return novoId;
    }
    
    const stmt = db.prepare(`
      INSERT INTO exercicios (treino_id, nome, tipo_treino, ordem)
      VALUES (?, ?, ?, ?)
    `);
    
    const info = stmt.run(
      exercicio.treino_id,
      exercicio.nome,
      exercicio.tipo_treino,
      exercicio.ordem
    );
    
    return info.lastInsertRowid as number;
  },
  
  obterExerciciosPorTreino: (treinoId: number): Exercicio[] => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockExercicios.filter(e => e.treino_id === treinoId);
    }
    
    const stmt = db.prepare('SELECT * FROM exercicios WHERE treino_id = ? ORDER BY tipo_treino, ordem');
    return stmt.all(treinoId);
  },
  
  obterExercicioPorId: (id: number): Exercicio | undefined => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockExercicios.find(e => e.id === id);
    }
    
    const stmt = db.prepare('SELECT * FROM exercicios WHERE id = ?');
    return stmt.get(id);
  },
  
  atualizarExercicio: (id: number, exercicio: Partial<Exercicio>): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockExercicios.findIndex(e => e.id === id);
      if (index !== -1) {
        mockExercicios[index] = { ...mockExercicios[index], ...exercicio, id };
        return true;
      }
      return false;
    }
    
    const campos = Object.keys(exercicio).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;
    
    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (exercicio as any)[campo]);
    
    const stmt = db.prepare(`UPDATE exercicios SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);
    
    return info.changes > 0;
  },
  
  excluirExercicio: (id: number): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockExercicios.findIndex(e => e.id === id);
      if (index !== -1) {
        mockExercicios.splice(index, 1);
        // Remover séries relacionadas
        mockSeries.splice(0, mockSeries.length, ...mockSeries.filter(s => s.exercicio_id !== id));
        return true;
      }
      return false;
    }
    
    const stmt = db.prepare('DELETE FROM exercicios WHERE id = ?');
    const info = stmt.run(id);
    
    return info.changes > 0;
  },
  
  // Métodos para Série
  criarSerie: (serie: Serie): number => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const novoId = mockSeries.length > 0 ? Math.max(...mockSeries.map(s => s.id || 0)) + 1 : 1;
      mockSeries.push({
        ...serie,
        id: novoId
      });
      return novoId;
    }
    
    const stmt = db.prepare(`
      INSERT INTO series (exercicio_id, semana, numero_serie, repeticoes, carga, volume_carga)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    const info = stmt.run(
      serie.exercicio_id,
      serie.semana,
      serie.numero_serie,
      serie.repeticoes,
      serie.carga,
      serie.volume_carga
    );
    
    return info.lastInsertRowid as number;
  },
  
  obterSeriesPorExercicio: (exercicioId: number): Serie[] => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockSeries.filter(s => s.exercicio_id === exercicioId);
    }
    
    const stmt = db.prepare('SELECT * FROM series WHERE exercicio_id = ? ORDER BY semana, numero_serie');
    return stmt.all(exercicioId);
  },
  
  obterSeriePorId: (id: number): Serie | undefined => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockSeries.find(s => s.id === id);
    }
    
    const stmt = db.prepare('SELECT * FROM series WHERE id = ?');
    return stmt.get(id);
  },
  
  atualizarSerie: (id: number, serie: Partial<Serie>): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockSeries.findIndex(s => s.id === id);
      if (index !== -1) {
        mockSeries[index] = { ...mockSeries[index], ...serie, id };
        return true;
      }
      return false;
    }
    
    const campos = Object.keys(serie).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;
    
    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (serie as any)[campo]);
    
    const stmt = db.prepare(`UPDATE series SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);
    
    return info.changes > 0;
  },
  
  excluirSerie: (id: number): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockSeries.findIndex(s => s.id === id);
      if (index !== -1) {
        mockSeries.splice(index, 1);
        return true;
      }
      return false;
    }
    
    const stmt = db.prepare('DELETE FROM series WHERE id = ?');
    const info = stmt.run(id);
    
    return info.changes > 0;
  },
  
  // Métodos para obter informações agregadas
  obterVolumeTotal: (exercicioId: number, semana: number): number => {
    const stmt = db.prepare('SELECT SUM(volume_carga) as volume_total FROM series WHERE exercicio_id = ? AND semana = ?');
    const resultado = stmt.get(exercicioId, semana) as { volume_total: number };
    
    return resultado.volume_total || 0;
  },
  
  obterProgressaoVolume: (exercicioId: number): { semana: number, volume_total: number }[] => {
    const stmt = db.prepare(`
      SELECT semana, SUM(volume_carga) as volume_total 
      FROM series 
      WHERE exercicio_id = ? 
      GROUP BY semana 
      ORDER BY semana
    `);
    
    return stmt.all(exercicioId) as { semana: number, volume_total: number }[];
  }
};

export default TreinoModel; 