import path from 'path';

// Importação condicional do better-sqlite3
let Database: any = null;
if (typeof window !== 'undefined' && 
    window.navigator && 
    /electron/i.test(window.navigator.userAgent)) {
  try {
    // eslint-disable-next-line
    Database = require('better-sqlite3');
  } catch (error) {
    console.error('Erro ao importar better-sqlite3:', error);
  }
}

const isElectron = typeof window !== 'undefined' && 
                  window.navigator && 
                  /electron/i.test(window.navigator.userAgent);
const isDevelopment = process.env.NODE_ENV === 'development';

// Importação condicional para evitar erros no navegador
let app: any = null;
if (isElectron) {
  try {
    // eslint-disable-next-line
    const electron = require('electron');
    app = electron.app;
  } catch (error) {
    console.log('Electron não disponível');
  }
}

// Determina o caminho do banco de dados
export const getDbPath = () => {
  const userDataPath = isDevelopment ? '.' : (app ? app.getPath('userData') : '.');
  return path.join(userDataPath, 'database.sqlite');
};

// Mock do banco de dados para desenvolvimento no navegador
const createMockDatabase = () => {
  const mockDb: any = {
    prepare: () => ({
      run: (...args: any[]) => ({ lastInsertRowid: Math.floor(Math.random() * 1000) + 1, changes: 1 }),
      get: () => null,
      all: () => []
    }),
    pragma: () => {},
    exec: () => {},
    close: () => {},
    transaction: (fn: Function) => fn
  };
  return mockDb;
};

export const initDatabase = () => {
  try {
    let db;
    
    // Se estiver em desenvolvimento no navegador, use o mock
    if (isDevelopment && !isElectron) {
      console.log('Inicializando banco de dados mock para desenvolvimento');
      db = createMockDatabase();
    } else if (Database) {
      // Se estiver no Electron, use o SQLite real
      db = new Database(getDbPath());
      
      // Habilita as foreign keys
      db.pragma('foreign_keys = ON');

      // Cria a tabela de clientes
      db.exec(`
        CREATE TABLE IF NOT EXISTS clientes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nome TEXT NOT NULL,
          data_nascimento TEXT,
          sexo TEXT,
          email TEXT,
          telefone TEXT,
          observacoes TEXT,
          data_cadastro TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Cria a tabela de avaliações físicas
      db.exec(`
        CREATE TABLE IF NOT EXISTS avaliacoes_fisicas (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cliente_id INTEGER NOT NULL,
          data_avaliacao TEXT NOT NULL,
          peso REAL NOT NULL,
          altura REAL NOT NULL,
          idade INTEGER,
          FOREIGN KEY (cliente_id) REFERENCES clientes (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de dobras cutâneas
      db.exec(`
        CREATE TABLE IF NOT EXISTS dobras_cutaneas (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          avaliacao_id INTEGER NOT NULL,
          subescapular REAL,
          tricipital REAL,
          bicipital REAL,
          axilar_media REAL,
          suprailiaca REAL,
          abdominal REAL,
          coxa REAL,
          panturrilha REAL,
          percentual_gordura REAL,
          FOREIGN KEY (avaliacao_id) REFERENCES avaliacoes_fisicas (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de medidas antropométricas
      db.exec(`
        CREATE TABLE IF NOT EXISTS medidas_antropometricas (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          avaliacao_id INTEGER NOT NULL,
          braco_direito REAL,
          braco_esquerdo REAL,
          antebraco_direito REAL,
          antebraco_esquerdo REAL,
          torax REAL,
          cintura REAL,
          abdomen REAL,
          quadril REAL,
          coxa_direita REAL,
          coxa_esquerda REAL,
          panturrilha_direita REAL,
          panturrilha_esquerda REAL,
          FOREIGN KEY (avaliacao_id) REFERENCES avaliacoes_fisicas (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de treinos
      db.exec(`
        CREATE TABLE IF NOT EXISTS treinos (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cliente_id INTEGER NOT NULL,
          nome TEXT NOT NULL,
          data_inicio TEXT NOT NULL,
          data_fim TEXT,
          observacoes TEXT,
          data_criacao TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (cliente_id) REFERENCES clientes (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de exercícios
      db.exec(`
        CREATE TABLE IF NOT EXISTS exercicios (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          treino_id INTEGER NOT NULL,
          nome TEXT NOT NULL,
          tipo_treino TEXT NOT NULL,
          ordem INTEGER NOT NULL,
          FOREIGN KEY (treino_id) REFERENCES treinos (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de séries
      db.exec(`
        CREATE TABLE IF NOT EXISTS series (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          exercicio_id INTEGER NOT NULL,
          semana INTEGER NOT NULL,
          numero_serie INTEGER NOT NULL,
          repeticoes INTEGER NOT NULL,
          carga REAL NOT NULL,
          volume_carga REAL NOT NULL,
          FOREIGN KEY (exercicio_id) REFERENCES exercicios (id) ON DELETE CASCADE
        )
      `);
    } else {
      console.log('SQLite não disponível, usando mock como fallback');
      db = createMockDatabase();
    }

    console.log('Banco de dados inicializado com sucesso!');
    return db;
  } catch (error) {
    console.error('Erro ao inicializar o banco de dados:', error);
    console.log('Usando banco de dados mock como fallback');
    return createMockDatabase();
  }
};

// Alias para compatibilidade
export const initializeDatabase = initDatabase; 