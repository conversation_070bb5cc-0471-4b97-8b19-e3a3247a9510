declare module 'better-sqlite3' {
  interface Statement {
    run(...params: any[]): { lastInsertRowid: number; changes: number };
    get(...params: any[]): any;
    all(...params: any[]): any[];
    iterate(...params: any[]): Iterable<any>;
  }

  interface Database {
    prepare(sql: string): Statement;
    exec(sql: string): void;
    pragma(pragma: string, simplify?: boolean): any;
    transaction<T extends Function>(fn: T): T;
    function(name: string, cb: Function): void;
    aggregate(name: string, options: { start?: any; step: Function; result?: Function }): void;
    backup(destination: string | Database, options?: { progress: Function }): Promise<void>;
    close(): void;
  }

  export default function(filename: string, options?: { readonly?: boolean; fileMustExist?: boolean; timeout?: number; verbose?: Function }): Database;
} 