/**
 * Formata uma data no formato ISO para o formato brasileiro (DD/MM/YYYY)
 * @param data Data no formato ISO
 * @returns Data formatada
 */
export const formatarData = (data: string | Date): string => {
  if (!data) return '';
  
  const dataObj = data instanceof Date ? data : new Date(data);
  return dataObj.toLocaleDateString('pt-BR');
};

/**
 * Formata um valor numérico para o formato de moeda brasileira
 * @param valor Valor a ser formatado
 * @returns Valor formatado
 */
export const formatarMoeda = (valor: number): string => {
  return valor.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
};

/**
 * Formata um valor numérico com a quantidade de casas decimais especificada
 * @param valor Valor a ser formatado
 * @param casasDecimais Quantidade de casas decimais
 * @returns Valor formatado
 */
export const formatarNumero = (valor: number, casasDecimais: number = 2): string => {
  return valor.toLocaleString('pt-BR', {
    minimumFractionDigits: casasDecimais,
    maximumFractionDigits: casasDecimais,
  });
}; 