import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Grid,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Paper,
  SelectChangeEvent
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { Exercicio } from '../../models/Treino';

interface ExercicioFormProps {
  exercicio?: Exercicio;
  treinoId: number;
  tipoTreinoAtivo: string;
  onSave: (exercicio: Exercicio) => void;
  onCancel: () => void;
}

const ExercicioForm: React.FC<ExercicioFormProps> = ({
  exercicio,
  treinoId,
  tipoTreinoAtivo,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<Partial<Exercicio>>({
    nome: '',
    tipo_treino: tipoTreinoAtivo,
    ordem: 1
  });
  
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    if (exercicio) {
      setFormData({
        ...exercicio
      });
    } else {
      setFormData({
        nome: '',
        tipo_treino: tipoTreinoAtivo,
        ordem: 1
      });
    }
  }, [exercicio, tipoTreinoAtivo]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | { name?: string; value: unknown }> | SelectChangeEvent
  ) => {
    const { name, value } = e.target;
    if (!name) return;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpa o erro quando o campo é alterado
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.nome || formData.nome.trim() === '') {
      newErrors.nome = 'O nome do exercício é obrigatório';
    }
    
    if (!formData.tipo_treino) {
      newErrors.tipo_treino = 'O tipo de treino é obrigatório';
    }
    
    if (!formData.ordem || formData.ordem < 1) {
      newErrors.ordem = 'A ordem deve ser um número positivo';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    const exercicioData: Exercicio = {
      ...formData as Exercicio,
      treino_id: treinoId
    };
    
    onSave(exercicioData);
  };

  const tiposTreino = ['A', 'B', 'C', 'D', 'E', 'F'];

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {exercicio ? 'Editar Exercício' : 'Novo Exercício'}
      </Typography>
      
      <Divider sx={{ mb: 3 }} />
      
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Nome do Exercício"
              name="nome"
              value={formData.nome || ''}
              onChange={handleChange}
              error={!!errors.nome}
              helperText={errors.nome}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.tipo_treino} required>
              <InputLabel id="tipo-treino-label">Tipo de Treino</InputLabel>
              <Select
                labelId="tipo-treino-label"
                name="tipo_treino"
                value={formData.tipo_treino || ''}
                onChange={handleChange}
                label="Tipo de Treino"
              >
                {tiposTreino.map((tipo) => (
                  <MenuItem key={tipo} value={tipo}>
                    Treino {tipo}
                  </MenuItem>
                ))}
              </Select>
              {errors.tipo_treino && <FormHelperText>{errors.tipo_treino}</FormHelperText>}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Ordem"
              name="ordem"
              type="number"
              value={formData.ordem || ''}
              onChange={handleChange}
              error={!!errors.ordem}
              helperText={errors.ordem}
              required
              InputProps={{ inputProps: { min: 1 } }}
            />
          </Grid>
          
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={onCancel}
              startIcon={<CancelIcon />}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
            >
              {exercicio ? 'Atualizar' : 'Salvar'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};

export default ExercicioForm; 