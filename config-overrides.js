const webpack = require('webpack');

module.exports = function override(config) {
  const fallback = {
    "process": require.resolve("process/browser"),
    "zlib": require.resolve("browserify-zlib"),
    "stream": require.resolve("stream-browserify"),
    "assert": require.resolve("assert"),
    "http": require.resolve("stream-http"),
    "https": require.resolve("https-browserify"),
    "os": require.resolve("os-browserify"),
    "url": require.resolve("url"),
    "path": require.resolve("path-browserify"),
    "fs": require.resolve("browserify-fs"),
    "crypto": require.resolve("crypto-browserify"),
    "buffer": require.resolve("buffer")
  };

  config.resolve.fallback = {
    ...config.resolve.fallback,
    ...fallback
  };

  config.plugins = (config.plugins || []).concat([
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    })
  ]);

  // Adicionar alias para o axios
  config.resolve.alias = {
    ...config.resolve.alias,
    'axios': require.resolve('axios')
  };

  return config;
}; 