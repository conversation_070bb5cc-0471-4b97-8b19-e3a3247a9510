import { createTheme } from '@mui/material/styles';
import { colors } from './colors';

export const theme = createTheme({
  palette: {
    primary: {
      main: colors.primary, // #FF6B35 - Laranja energético
      light: colors.energy[300], // #FFB584 - Laranja claro
      dark: colors.energy[700], // #CC4A21 - Laranja escuro
      contrastText: colors.white,
    },
    secondary: {
      main: colors.secondary, // #2C3E50 - Azul profissional
      light: colors.professional[400], // #6C757D - Cinza médio
      dark: colors.professional[800], // #1A252F - Azul muito escuro
      contrastText: colors.white,
    },
    error: {
      main: colors.error, // #E74C3C - Vermelho suave
      light: '#F1948A',
      dark: '#C0392B',
    },
    warning: {
      main: colors.warning, // #F39C12 - Laranja suave
      light: '#F8C471',
      dark: '#D68910',
    },
    info: {
      main: colors.info, // #3498DB - Azul informativo
      light: '#85C1E9',
      dark: '#2874A6',
    },
    success: {
      main: colors.success, // #27AE60 - Verde saúde
      light: colors.health[300], // #86EFAC - Verde claro
      dark: colors.health[700], // #0F7B6C - Verde escuro
    },
    background: {
      default: colors.professional[50], // #F8F9FA - Fundo muito claro
      paper: colors.white, // #FFFFFF - Branco puro
    },
    text: {
      primary: colors.text.primary, // #2C3E50 - Azul escuro
      secondary: colors.text.secondary, // #34495E - Azul médio
      disabled: colors.text.disabled, // #BDC3C7 - Cinza claro
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    subtitle1: {
      fontSize: '1rem',
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
    subtitle2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
      letterSpacing: '0.00714em',
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
      letterSpacing: '0.01071em',
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      letterSpacing: '0.02857em',
      textTransform: 'none',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          textTransform: 'none',
          fontWeight: 500,
          padding: '10px 20px',
          fontSize: '0.875rem',
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: colors.shadows.md,
            transform: 'translateY(-1px)',
          },
        },
        containedPrimary: {
          background: colors.gradients.primary,
          '&:hover': {
            background: colors.gradients.energy,
            boxShadow: colors.shadows.energy,
          },
        },
        containedSecondary: {
          background: colors.gradients.secondary,
          '&:hover': {
            background: colors.professional[600],
            boxShadow: colors.shadows.lg,
          },
        },
        outlined: {
          borderWidth: '1.5px',
          '&:hover': {
            borderWidth: '1.5px',
            backgroundColor: `${colors.primary}${colors.opacity[5]}`,
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: colors.white,
        },
        rounded: {
          borderRadius: 12,
        },
        elevation1: {
          boxShadow: colors.shadows.sm,
        },
        elevation2: {
          boxShadow: colors.shadows.md,
        },
        elevation3: {
          boxShadow: colors.shadows.lg,
        },
        elevation4: {
          boxShadow: colors.shadows.xl,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: colors.shadows.sm,
          border: `1px solid ${colors.professional[100]}`,
          '&:hover': {
            boxShadow: colors.shadows.md,
            transform: 'translateY(-2px)',
            transition: 'all 0.2s ease-in-out',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${colors.professional[200]}`,
          padding: '12px 16px',
        },
        head: {
          fontWeight: 600,
          backgroundColor: colors.professional[50],
          color: colors.text.primary,
          fontSize: '0.875rem',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 10,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.primary,
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.primary,
              borderWidth: '2px',
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
        },
        colorPrimary: {
          backgroundColor: `${colors.primary}${colors.opacity[10]}`,
          color: colors.primary,
        },
        colorSecondary: {
          backgroundColor: `${colors.secondary}${colors.opacity[10]}`,
          color: colors.secondary,
        },
      },
    },
  },
});

export default theme; 