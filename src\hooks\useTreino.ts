import { useState } from 'react';
import { Treino } from '../models/Treino';
import { Exercicio } from '../models/Exercicio';
import { Serie } from '../models/Serie';
import api from '../services/api';

export const useTreino = () => {
  const [treinos, setTreinos] = useState<Treino[]>([]);
  const [treinoSelecionado, setTreinoSelecionado] = useState<Treino | null>(null);
  const [exercicios, setExercicios] = useState<Exercicio[]>([]);
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const carregarTreinosPorCliente = async (clienteId: number) => {
    setCarregando(true);
    setErro(null);
    try {
      const response = await api.get(`/treinos/cliente/${clienteId}`);
      setTreinos(response.data);
    } catch (error) {
      setErro('Erro ao carregar treinos');
      console.error('Erro ao carregar treinos:', error);
    } finally {
      setCarregando(false);
    }
  };

  const carregarExerciciosPorTreino = async (treinoId: number, tipoTreino: string) => {
    setCarregando(true);
    setErro(null);
    try {
      const response = await api.get(`/exercicios/treino/${treinoId}?tipo=${tipoTreino}`);
      setExercicios(response.data);
    } catch (error) {
      setErro('Erro ao carregar exercícios');
      console.error('Erro ao carregar exercícios:', error);
    } finally {
      setCarregando(false);
    }
  };

  const criarTreino = async (treino: Partial<Treino>) => {
    setCarregando(true);
    setErro(null);
    try {
      const response = await api.post('/treinos', treino);
      setTreinos([...treinos, response.data]);
      return response.data;
    } catch (error) {
      setErro('Erro ao criar treino');
      console.error('Erro ao criar treino:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  };

  const excluirTreino = async (id: number) => {
    setCarregando(true);
    setErro(null);
    try {
      await api.delete(`/treinos/${id}`);
      setTreinos(treinos.filter(treino => treino.id !== id));
      if (treinoSelecionado?.id === id) {
        setTreinoSelecionado(null);
      }
    } catch (error) {
      setErro('Erro ao excluir treino');
      console.error('Erro ao excluir treino:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  };

  const selecionarTreino = (treino: Treino | null) => {
    setTreinoSelecionado(treino);
  };

  const criarExercicio = async (exercicio: Partial<Exercicio>) => {
    setCarregando(true);
    setErro(null);
    try {
      const response = await api.post('/exercicios', exercicio);
      setExercicios([...exercicios, response.data]);
      return response.data;
    } catch (error) {
      setErro('Erro ao criar exercício');
      console.error('Erro ao criar exercício:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  };

  const excluirExercicio = async (id: number) => {
    setCarregando(true);
    setErro(null);
    try {
      await api.delete(`/exercicios/${id}`);
      setExercicios(exercicios.filter(exercicio => exercicio.id !== id));
    } catch (error) {
      setErro('Erro ao excluir exercício');
      console.error('Erro ao excluir exercício:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  };

  const criarSerie = async (serie: Serie) => {
    setCarregando(true);
    setErro(null);
    try {
      const response = await api.post('/series', serie);
      return response.data;
    } catch (error) {
      setErro('Erro ao criar série');
      console.error('Erro ao criar série:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  };

  const obterSeriesPorExercicio = async (exercicioId: number, semana: number) => {
    setCarregando(true);
    setErro(null);
    try {
      const response = await api.get(`/series/exercicio/${exercicioId}?semana=${semana}`);
      return response.data;
    } catch (error) {
      setErro('Erro ao carregar séries');
      console.error('Erro ao carregar séries:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  };

  return {
    treinos,
    treinoSelecionado,
    exercicios,
    carregando,
    erro,
    carregarTreinosPorCliente,
    carregarExerciciosPorTreino,
    criarTreino,
    excluirTreino,
    selecionarTreino,
    criarExercicio,
    excluirExercicio,
    criarSerie,
    obterSeriesPorExercicio
  };
}; 