import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Grid,
  Typography,
  Paper,
  Divider,
  MenuItem,
  Snackbar,
  Alert,
  Tooltip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  SelectChangeEvent,
  Stack,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import MonitorWeightIcon from '@mui/icons-material/MonitorWeight';
import StraightenIcon from '@mui/icons-material/Straighten';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ScaleIcon from '@mui/icons-material/Scale';
import HeightIcon from '@mui/icons-material/Height';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import Fitness<PERSON>enterIcon from '@mui/icons-material/FitnessCenter';
import Check<PERSON>ircleIcon from '@mui/icons-material/CheckCircle';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { AvaliacaoFisica, DobrasCutaneas } from '../../models/AvaliacaoFisica';
import { Cliente } from '../../models/Cliente';
import { useAppContext } from '../../contexts/AppContext';
import { useAvaliacaoFisica } from '../../contexts/AvaliacaoFisicaContext';
import { useCliente } from '../../contexts/ClienteContext';
import { formatarData } from '../../utils/formatadores';
import type { MedidasAntropometricas as MedidasAntropometricasModel } from '../../models/AvaliacaoFisica';

interface AvaliacaoFisicaFormProps {
  avaliacaoParaEditar?: AvaliacaoFisica;
  clienteId?: number;
  onSalvar?: () => void;
  onCancelar?: () => void;
}

interface DistribuicaoGordura {
  tipo: string;
  risco: string;
  recomendacoes: string[];
}

interface BiotipoAnalise {
  tipo: 'Ectomorfo' | 'Mesomorfo' | 'Endomorfo';
  caracteristicas: string[];
  recomendacoes: string[];
}

interface TMBAnalise {
  tmb: number;
  classificacao: string;
  recomendacoes: string[];
}

interface GETAnalise {
  get: number;
  nivelAtividade: string;
  classificacao: string;
  recomendacoes: string[];
}

interface AjusteAtividadeAnalise {
  fatorAjuste: number;
  intensidadeExercicios: string;
  frequenciaSemanal: number;
  tipoTrabalho: string;
  recomendacoes: string[];
}

interface MetasNutricionais {
  caloriasAlvo: number;
  proteinas: number;
  carboidratos: number;
  gorduras: number;
  refeicoesDiarias: number;
  distribuicaoMacros: {
    proteinas: number;
    carboidratos: number;
    gorduras: number;
  };
  recomendacoes: string[];
}

interface VolumeAnalise {
  volumeTotal: number;
  volumePorGrupoMuscular: {
    [key: string]: number;
  };
  volumePorExercicio: {
    [key: string]: number;
  };
  mediaCargaPorExercicio: {
    [key: string]: number;
  };
  classificacaoVolume: string;
  recomendacoes: string[];
}

interface ProgressaoAnalise {
  progressaoPorExercicio: {
    [key: string]: {
      cargaInicial: number;
      cargaAtual: number;
      progressaoTotal: number;
      progressaoSemanal: number;
      progressaoIdeal: number;
      status: 'abaixo' | 'adequada' | 'acima';
    }
  };
  progressaoGeral: {
    mediaProgressao: number;
    exerciciosEstagnados: string[];
    exerciciosRapidos: string[];
    exerciciosLentos: string[];
  };
  recomendacoes: string[];
}

interface PeriodizacaoAnalise {
  fase: 'adaptacao' | 'hipertrofia' | 'forca' | 'resistencia' | 'pico' | 'transicao';
  semanaAtual: number;
  duracaoFase: number;
  volumeSemanal: {
    series: number;
    repeticoes: number;
    intensidade: number;
  };
  distribuicaoGruposMusculares: {
    [grupo: string]: {
      frequenciaSemanal: number;
      volumeSemanal: number;
      exerciciosPrincipais: string[];
    }
  };
  parametrosTreino: {
    tempoRecuperacao: number;
    intensidadeMedia: number;
    densidadeTreino: number;
    metodosIntensificacao: string[];
  };
  recomendacoes: string[];
}

interface HistoricoEvolucaoAnalise {
  evolucaoGeral: {
    periodoTotal: number; // em semanas
    volumeAcumulado: number;
    progressaoMedia: number;
    tempoTotalTreino: number; // em minutos
  };
  evolucaoPorGrupoMuscular: {
    [grupo: string]: {
      volumeInicial: number;
      volumeAtual: number;
      progressaoTotal: number;
      exerciciosMaisEvoluidos: string[];
      exerciciosMenosEvoluidos: string[];
    }
  };
  marcosImportantes: Array<{
    data: Date;
    tipo: 'PR' | 'Volume' | 'Técnica' | 'Lesão' | 'Recuperação';
    descricao: string;
    exercicio?: string;
    valor?: number;
  }>;
  indicadoresDesempenho: {
    consistencia: number; // 0-100%
    intensidadeMedia: number;
    volumeMedioPorSemana: number;
    tempoMedioPorTreino: number;
  };
  tendencias: {
    volumeProgressao: 'crescente' | 'estavel' | 'decrescente';
    intensidadeProgressao: 'crescente' | 'estavel' | 'decrescente';
    frequenciaProgressao: 'crescente' | 'estavel' | 'decrescente';
  };
  recomendacoes: string[];
}

type ObjetivoTipo = 'manutenção' | 'perda' | 'ganho';

interface MedidasAntropometricas {
  avaliacao_id: number;
  braco_direito: number;
  braco_esquerdo: number;
  antebraco_direito: number;
  antebraco_esquerdo: number;
  peitoral: number;
  cintura: number;
  abdomen: number;
  quadril: number;
  coxa_direita: number;
  coxa_esquerda: number;
  panturrilha_direita: number;
  panturrilha_esquerda: number;
}

const AvaliacaoFisicaForm: React.FC<AvaliacaoFisicaFormProps> = ({
  avaliacaoParaEditar,
  clienteId,
  onSalvar,
  onCancelar
}): JSX.Element => {
  const { clientes, carregarClientes } = useCliente();
  const { criarAvaliacao, atualizarAvaliacao, carregando, erro } = useAvaliacaoFisica();
  const [mensagem, setMensagem] = useState<string>('');
  const [tipoMensagem, setTipoMensagem] = useState<'success' | 'error'>('success');
  const [mostrarMensagem, setMostrarMensagem] = useState(false);

  const [avaliacao, setAvaliacao] = useState<AvaliacaoFisica>({
    cliente_id: clienteId || 0,
    data_avaliacao: new Date().toISOString().split('T')[0],
    peso: 0,
    altura: 0,
    idade: 0,
    nivelAtividade: 'Sedentário',
    intensidadeExercicios: 'Leve',
    frequenciaSemanal: 0,
    tipoTrabalho: 'Sedentário',
    objetivo: 'manutenção',
    percentualGordura: 0,
    dobras_cutaneas: {
      avaliacao_id: 0,
      peitoral: 0,
      tricipital: 0,
      bicipital: 0,
      axilar_media: 0,
      suprailiaca: 0,
      abdominal: 0,
      coxa: 0,
      panturrilha: 0,
      percentual_gordura: 0
    },
    medidas_antropometricas: {
      avaliacao_id: 0,
      braco_direito: 0,
      braco_esquerdo: 0,
      antebraco_direito: 0,
      antebraco_esquerdo: 0,
      peitoral: 0,
      cintura: 0,
      abdomen: 0,
      quadril: 0,
      coxa_direita: 0,
      coxa_esquerda: 0,
      panturrilha_direita: 0,
      panturrilha_esquerda: 0
    }
  });

  useEffect(() => {
    if (avaliacaoParaEditar) {
      setAvaliacao(avaliacaoParaEditar);
    }
  }, [avaliacaoParaEditar]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string | number>,
    tipo: string
  ) => {
    const { name, value } = e.target;
    
    if (tipo === 'avaliacao') {
      setAvaliacao(prev => ({
        ...prev,
        [name]: value
      }));
    } else if (tipo === 'dobras' && avaliacao.dobras_cutaneas) {
      setAvaliacao(prev => ({
        ...prev,
        dobras_cutaneas: {
          ...prev.dobras_cutaneas!,
          [name]: Number(value)
        }
      }));
    } else if (tipo === 'medidas' && avaliacao.medidas_antropometricas) {
      setAvaliacao(prev => ({
        ...prev,
        medidas_antropometricas: {
          ...prev.medidas_antropometricas!,
          [name]: Number(value)
        }
      }));
    }
  };

  const calcularPercentualGordura = () => {
    if (!avaliacao.dobras_cutaneas || !avaliacao.medidas_antropometricas) {
      return 0;
    }

    // Soma das 7 dobras (em mm)
    const somaDobras = 
      avaliacao.dobras_cutaneas.peitoral +
      avaliacao.dobras_cutaneas.tricipital +
      avaliacao.dobras_cutaneas.axilar_media +
      avaliacao.dobras_cutaneas.suprailiaca +
      avaliacao.dobras_cutaneas.abdominal +
      avaliacao.dobras_cutaneas.coxa +
      avaliacao.dobras_cutaneas.panturrilha;

    // Densidade corporal (fórmula de Jackson & Pollock)
    let densidadeCorporal;
    
    // Verificar se é homem ou mulher baseado no cliente selecionado
    const clienteSelecionado = clientes.find(c => c.id === avaliacao.cliente_id);
    const sexo = clienteSelecionado?.sexo || 'M';
    
    if (sexo === 'M') {
      // Fórmula para homens
      densidadeCorporal = 1.112 - (0.00043499 * somaDobras) + 
                          (0.00000055 * somaDobras * somaDobras) - 
                          (0.00028826 * avaliacao.idade);
    } else {
      // Fórmula para mulheres
      densidadeCorporal = 1.097 - (0.00046971 * somaDobras) + 
                          (0.00000056 * somaDobras * somaDobras) - 
                          (0.00012828 * avaliacao.idade);
    }
    
    // Cálculo do percentual de gordura usando a equação de Siri
    const percentualGordura = ((4.95 / densidadeCorporal) - 4.50) * 100;
    
    setAvaliacao(prev => ({
      ...prev,
      dobras_cutaneas: {
        ...prev.dobras_cutaneas!,
        percentual_gordura: Number(percentualGordura.toFixed(2))
      }
    }));
  };

  const analisarDistribuicaoGordura = (
    cintura: number,
    quadril: number,
    sexo: string
  ): DistribuicaoGordura => {
    const relacaoCinturaQuadril = cintura / quadril;
    
    if (sexo === 'M') {
      if (relacaoCinturaQuadril < 0.85) {
        return {
          tipo: 'Ginóide',
          risco: 'Baixo',
          recomendacoes: [
            'Manter atividade física regular',
            'Priorizar exercícios para membros inferiores',
            'Manter alimentação balanceada'
          ]
        };
      } else if (relacaoCinturaQuadril < 0.9) {
        return {
          tipo: 'Intermediário',
          risco: 'Moderado',
          recomendacoes: [
            'Aumentar frequência de exercícios aeróbicos',
            'Controlar ingestão de carboidratos simples',
            'Incluir exercícios para core'
          ]
        };
      } else {
        return {
          tipo: 'Andróide',
          risco: 'Alto',
          recomendacoes: [
            'Priorizar redução da gordura abdominal',
            'Realizar exercícios aeróbicos diários',
            'Consultar nutricionista para reeducação alimentar',
            'Monitorar pressão arterial e glicemia'
          ]
        };
      }
    } else {
      if (relacaoCinturaQuadril < 0.75) {
        return {
          tipo: 'Ginóide',
          risco: 'Baixo',
          recomendacoes: [
            'Manter atividade física regular',
            'Priorizar exercícios para membros inferiores',
            'Manter alimentação balanceada'
          ]
        };
      } else if (relacaoCinturaQuadril < 0.8) {
        return {
          tipo: 'Intermediário',
          risco: 'Moderado',
          recomendacoes: [
            'Aumentar frequência de exercícios aeróbicos',
            'Controlar ingestão de carboidratos simples',
            'Incluir exercícios para core'
          ]
        };
      } else {
        return {
          tipo: 'Andróide',
          risco: 'Alto',
          recomendacoes: [
            'Priorizar redução da gordura abdominal',
            'Realizar exercícios aeróbicos diários',
            'Consultar nutricionista para reeducação alimentar',
            'Monitorar pressão arterial e glicemia'
          ]
        };
      }
    }
  };

  const analisarBiotipo = (
    altura: number,
    peso: number,
    percentualGordura: number,
    circunferenciaPulso: number,
    sexo: string
  ): BiotipoAnalise => {
    // Índice de Massa Corporal (IMC)
    const imc = peso / ((altura / 100) * (altura / 100));
    
    // Índice de Robustez (circunferência do pulso em relação à altura)
    const indiceRobustez = (circunferenciaPulso / altura) * 100;
    
    // Classificação inicial baseada no IMC e percentual de gordura
    let tipo: 'Ectomorfo' | 'Mesomorfo' | 'Endomorfo';
    
    if (imc < 20 && percentualGordura < 15) {
      tipo = 'Ectomorfo';
    } else if (imc > 25 && percentualGordura > 25) {
      tipo = 'Endomorfo';
    } else {
      tipo = 'Mesomorfo';
    }
    
    // Ajuste baseado no índice de robustez
    if (sexo === 'M') {
      if (indiceRobustez > 3.1) tipo = 'Mesomorfo';
      if (indiceRobustez < 2.7) tipo = 'Ectomorfo';
    } else {
      if (indiceRobustez > 2.9) tipo = 'Mesomorfo';
      if (indiceRobustez < 2.5) tipo = 'Ectomorfo';
    }
    
    const caracteristicas = {
      Ectomorfo: [
        'Estrutura corporal longilínea',
        'Dificuldade para ganhar peso',
        'Metabolismo acelerado',
        'Baixo percentual de gordura natural',
        'Ossatura fina'
      ],
      Mesomorfo: [
        'Estrutura corporal atlética',
        'Facilidade para ganhar massa muscular',
        'Metabolismo equilibrado',
        'Boa resposta ao treinamento',
        'Ossatura média a grande'
      ],
      Endomorfo: [
        'Estrutura corporal mais robusta',
        'Tendência a acumular gordura',
        'Metabolismo mais lento',
        'Maior força natural',
        'Ossatura grande'
      ]
    };
    
    const recomendacoes = {
      Ectomorfo: [
        'Priorizar exercícios compostos com cargas moderadas a pesadas',
        'Aumentar gradualmente o volume de treino',
        'Focar em séries de 6-8 repetições',
        'Períodos de descanso adequados entre as séries (1-2 minutos)',
        'Dieta hipercalórica com bom aporte de carboidratos'
      ],
      Mesomorfo: [
        'Variar entre treinos de força e hipertrofia',
        'Manter intensidade moderada a alta',
        'Trabalhar com séries de 8-12 repetições',
        'Descanso moderado entre séries (45-90 segundos)',
        'Dieta balanceada com proporções equilibradas de macronutrientes'
      ],
      Endomorfo: [
        'Incluir exercícios aeróbicos regulares',
        'Priorizar circuitos e superséries',
        'Trabalhar com séries de 12-15 repetições',
        'Descanso reduzido entre séries (30-45 segundos)',
        'Dieta com déficit calórico moderado e alto teor proteico'
      ]
    };
    
    return {
      tipo,
      caracteristicas: caracteristicas[tipo],
      recomendacoes: recomendacoes[tipo]
    };
  };

  const calcularTMB = (
    peso: number,
    altura: number,
    idade: number,
    percentualGordura: number,
    sexo: string
  ): TMBAnalise => {
    // Cálculo da massa magra
    const massaMagra = peso * (1 - (percentualGordura / 100));
    
    // TMB usando a fórmula de Cunningham (específica para atletas e praticantes de musculação)
    const tmb = 500 + (22 * massaMagra);
    
    // Classificação baseada no TMB e sexo
    let classificacao: 'Baixo' | 'Moderado' | 'Alto';
    if (sexo === 'M') {
      if (tmb < 1600) classificacao = 'Baixo';
      else if (tmb < 1800) classificacao = 'Moderado';
      else classificacao = 'Alto';
    } else {
      if (tmb < 1400) classificacao = 'Baixo';
      else if (tmb < 1600) classificacao = 'Moderado';
      else classificacao = 'Alto';
    }
    
    // Recomendações baseadas na classificação e sexo
    const recomendacoesPorSexo = {
      M: {
        Baixo: [
          'Aumentar gradualmente a ingestão calórica',
          'Priorizar refeições frequentes ao longo do dia',
          'Incluir alimentos calóricos e nutritivos',
          'Monitorar a composição corporal regularmente',
          'Considerar suplementação nutricional supervisionada'
        ],
        Moderado: [
          'Manter a ingestão calórica atual',
          'Distribuir macronutrientes de forma equilibrada',
          'Ajustar calorias conforme objetivos específicos',
          'Monitorar energia e disposição para treinos',
          'Manter hidratação adequada'
        ],
        Alto: [
          'Aumentar ingestão proteica para preservar massa magra',
          'Fracionar refeições em 5-6 vezes ao dia',
          'Priorizar carboidratos complexos',
          'Ajustar calorias conforme nível de atividade',
          'Considerar acompanhamento nutricional especializado'
        ]
      },
      F: {
        Baixo: [
          'Aumentar ingestão calórica gradualmente com foco em nutrientes',
          'Incluir proteínas magras em todas as refeições',
          'Adicionar gorduras boas (azeite, abacate, oleaginosas)',
          'Monitorar ciclo menstrual e ajustar conforme fase',
          'Considerar suplementação de ferro e cálcio'
        ],
        Moderado: [
          'Manter distribuição equilibrada de macronutrientes',
          'Priorizar alimentos ricos em ferro e vitamina B12',
          'Ajustar calorias conforme fase do ciclo menstrual',
          'Manter hidratação adequada (mínimo 2L/dia)',
          'Incluir lanches nutritivos entre refeições principais'
        ],
        Alto: [
          'Fracionar em 5-6 refeições balanceadas',
          'Aumentar consumo de proteínas magras',
          'Incluir carboidratos complexos pré e pós-treino',
          'Monitorar sinais de overtraining',
          'Considerar acompanhamento nutricional especializado'
        ]
      }
    };
    
    return {
      tmb: Math.round(tmb),
      classificacao,
      recomendacoes: recomendacoesPorSexo[sexo === 'M' ? 'M' : 'F'][classificacao]
    };
  };

  const calcularGET = (
    tmb: number,
    nivelAtividade: 'Sedentário' | 'Levemente Ativo' | 'Moderadamente Ativo' | 'Muito Ativo' | 'Extremamente Ativo',
    sexo: string
  ): GETAnalise => {
    // Fatores de atividade baseados no nível de atividade física
    const fatoresAtividade = {
      'Sedentário': 1.2,
      'Levemente Ativo': 1.375,
      'Moderadamente Ativo': 1.55,
      'Muito Ativo': 1.725,
      'Extremamente Ativo': 1.9
    };

    // Cálculo do GET
    const get = Math.round(tmb * fatoresAtividade[nivelAtividade]);
    
    // Classificação baseada no GET e sexo
    let classificacao: string;
    if (sexo === 'M') {
      if (get < 2000) classificacao = 'Baixo';
      else if (get < 2500) classificacao = 'Moderado';
      else classificacao = 'Alto';
    } else {
      if (get < 1800) classificacao = 'Baixo';
      else if (get < 2200) classificacao = 'Moderado';
      else classificacao = 'Alto';
    }

    // Recomendações baseadas no nível de atividade e classificação
    const recomendacoesPorNivel = {
      'Sedentário': [
        'Aumentar gradualmente o nível de atividade física',
        'Começar com caminhadas leves diárias',
        'Incluir exercícios de mobilidade',
        'Monitorar a frequência cardíaca durante atividades',
        'Manter boa hidratação mesmo em repouso'
      ],
      'Levemente Ativo': [
        'Aumentar a intensidade das atividades atuais',
        'Incluir exercícios resistidos 2-3x por semana',
        'Manter atividades aeróbicas leves',
        'Planejar refeições pré e pós-treino',
        'Monitorar a recuperação entre atividades'
      ],
      'Moderadamente Ativo': [
        'Otimizar a nutrição para suportar o nível de atividade',
        'Incluir períodos de recuperação adequados',
        'Variar entre exercícios de alta e baixa intensidade',
        'Manter acompanhamento da progressão',
        'Considerar suplementação específica para atividade'
      ],
      'Muito Ativo': [
        'Priorizar recuperação e qualidade do sono',
        'Implementar estratégias de periodização',
        'Monitorar sinais de overtraining',
        'Ajustar macronutrientes conforme demanda',
        'Manter acompanhamento profissional regular'
      ],
      'Extremamente Ativo': [
        'Implementar estratégias avançadas de recuperação',
        'Monitorar marcadores de fadiga e estresse',
        'Ajustar nutrição para alta demanda energética',
        'Considerar suplementação específica',
        'Manter acompanhamento médico e nutricional'
      ]
    };

    return {
      get,
      nivelAtividade,
      classificacao,
      recomendacoes: recomendacoesPorNivel[nivelAtividade]
    };
  };

  const calcularAjusteAtividade = (
    intensidadeExercicios: 'Leve' | 'Moderada' | 'Alta' | 'Muito Alta',
    frequenciaSemanal: number,
    tipoTrabalho: 'Sedentário' | 'Leve' | 'Moderado' | 'Intenso'
  ): AjusteAtividadeAnalise => {
    // Fatores base para intensidade dos exercícios
    const fatoresIntensidade = {
      'Leve': 0.1,
      'Moderada': 0.2,
      'Alta': 0.3,
      'Muito Alta': 0.4
    };

    // Fatores para frequência semanal (0-7 dias)
    const fatorFrequencia = Math.min(frequenciaSemanal, 7) * 0.05;

    // Fatores para tipo de trabalho
    const fatoresTrabalho = {
      'Sedentário': 0,
      'Leve': 0.1,
      'Moderado': 0.2,
      'Intenso': 0.3
    };

    // Cálculo do fator de ajuste total
    const fatorAjuste = 1 + fatoresIntensidade[intensidadeExercicios] + fatorFrequencia + fatoresTrabalho[tipoTrabalho];

    // Recomendações baseadas nos parâmetros
    const recomendacoes = [
      `Considerar aumento de ${Math.round((fatorAjuste - 1) * 100)}% nas calorias em dias de treino`,
      `Ajustar hidratação para ${Math.round(35 * fatorAjuste)}ml/kg de peso corporal`,
      `Distribuir macronutrientes considerando a intensidade ${intensidadeExercicios.toLowerCase()} dos exercícios`,
      `Planejar refeições pré e pós-treino para ${frequenciaSemanal}x por semana`,
      `Adaptar nutrição ao tipo de trabalho ${tipoTrabalho.toLowerCase()}`
    ];

    // Recomendações específicas por intensidade
    if (intensidadeExercicios === 'Alta' || intensidadeExercicios === 'Muito Alta') {
      recomendacoes.push(
        'Considerar suplementação de eletrólitos',
        'Implementar estratégias de recuperação avançadas'
      );
    }

    // Recomendações para frequência alta
    if (frequenciaSemanal >= 5) {
      recomendacoes.push(
        'Monitorar sinais de overtraining',
        'Implementar periodização na intensidade'
      );
    }

    // Recomendações para trabalho intenso
    if (tipoTrabalho === 'Intenso') {
      recomendacoes.push(
        'Priorizar recuperação entre sessões de trabalho e treino',
        'Considerar divisão dos treinos em dois momentos do dia'
      );
    }

    return {
      fatorAjuste,
      intensidadeExercicios,
      frequenciaSemanal,
      tipoTrabalho,
      recomendacoes
    };
  };

  const calcularMetasNutricionais = (
    get: number,
    sexo: string,
    objetivo: ObjetivoTipo,
    percentualGordura: number
  ): MetasNutricionais => {
    let caloriasAlvo = get;
    
    // Ajuste calórico baseado no objetivo
    switch (objetivo) {
      case 'perda':
        caloriasAlvo *= 0.8; // Déficit de 20%
        break;
      case 'ganho':
        caloriasAlvo *= 1.1; // Superávit de 10%
        break;
      default:
        // Manutenção mantém o GET
        break;
    }

    // Cálculo de macronutrientes baseado no peso ideal e objetivo
    const pesoIdeal = sexo === 'F' ? 
      (avaliacao.altura * avaliacao.altura * 21.5) : // IMC alvo de 21.5 para mulheres
      (avaliacao.altura * avaliacao.altura * 23.0);  // IMC alvo de 23.0 para homens

    let distribuicaoMacros = {
      proteinas: 0,
      carboidratos: 0,
      gorduras: 0
    };

    // Ajuste da distribuição de macros baseado no objetivo e % de gordura
    if (objetivo === 'perda') {
      distribuicaoMacros = {
        proteinas: 0.40, // 40% proteína
        carboidratos: 0.35, // 35% carboidrato
        gorduras: 0.25 // 25% gordura
      };
    } else if (objetivo === 'ganho') {
      distribuicaoMacros = {
        proteinas: 0.30, // 30% proteína
        carboidratos: 0.50, // 50% carboidrato
        gorduras: 0.20 // 20% gordura
      };
    } else {
      distribuicaoMacros = {
        proteinas: 0.35, // 35% proteína
        carboidratos: 0.40, // 40% carboidrato
        gorduras: 0.25 // 25% gordura
      };
    }

    // Cálculo dos gramas de cada macronutriente
    const proteinas = (caloriasAlvo * distribuicaoMacros.proteinas) / 4; // 4 kcal/g
    const carboidratos = (caloriasAlvo * distribuicaoMacros.carboidratos) / 4; // 4 kcal/g
    const gorduras = (caloriasAlvo * distribuicaoMacros.gorduras) / 9; // 9 kcal/g

    // Número recomendado de refeições
    const refeicoesDiarias = objetivo === 'ganho' ? 6 : 5;

    // Recomendações personalizadas
    const recomendacoes = [
      `Consumir ${Math.round(caloriasAlvo)} kcal por dia`,
      `Distribuir as calorias em ${refeicoesDiarias} refeições`,
      `Proteínas: ${Math.round(proteinas)}g (${Math.round(proteinas / refeicoesDiarias)}g por refeição)`,
      `Carboidratos: ${Math.round(carboidratos)}g (${Math.round(carboidratos / refeicoesDiarias)}g por refeição)`,
      `Gorduras: ${Math.round(gorduras)}g (${Math.round(gorduras / refeicoesDiarias)}g por refeição)`,
      'Priorizar proteínas magras e carboidratos complexos',
      'Manter boa hidratação (30-35ml/kg de peso)',
      'Consumir vegetais em todas as refeições principais',
      sexo === 'F' ? 'Atenção especial à ingestão de ferro e cálcio' : 'Atenção à qualidade das gorduras consumidas'
    ];

    if (percentualGordura > 25 && sexo === 'M' || percentualGordura > 30 && sexo === 'F') {
      recomendacoes.push('Priorizar alimentos termogênicos e fibras');
      recomendacoes.push('Considerar jejum intermitente sob orientação');
    }

    return {
      caloriasAlvo: Math.round(caloriasAlvo),
      proteinas: Math.round(proteinas),
      carboidratos: Math.round(carboidratos),
      gorduras: Math.round(gorduras),
      refeicoesDiarias,
      distribuicaoMacros,
      recomendacoes
    };
  };

  const calcularVolumeTreino = (
    exercicios: Array<{
      nome: string;
      grupoMuscular: string;
      series: number;
      repeticoes: number;
      carga: number;
    }>,
    nivelTreinamento: 'iniciante' | 'intermediario' | 'avancado',
    objetivo: 'hipertrofia' | 'força' | 'resistencia'
  ): VolumeAnalise => {
    // Inicializa os objetos para armazenar volumes
    const volumePorGrupoMuscular: { [key: string]: number } = {};
    const volumePorExercicio: { [key: string]: number } = {};
    const mediaCargaPorExercicio: { [key: string]: number } = {};
    let volumeTotal = 0;

    // Calcula o volume para cada exercício
    exercicios.forEach(exercicio => {
      // Volume = Séries x Repetições x Carga
      const volumeExercicio = exercicio.series * exercicio.repeticoes * exercicio.carga;
      
      // Atualiza volume por exercício
      volumePorExercicio[exercicio.nome] = volumeExercicio;
      
      // Atualiza média de carga por exercício
      mediaCargaPorExercicio[exercicio.nome] = exercicio.carga;
      
      // Atualiza volume por grupo muscular
      if (volumePorGrupoMuscular[exercicio.grupoMuscular]) {
        volumePorGrupoMuscular[exercicio.grupoMuscular] += volumeExercicio;
      } else {
        volumePorGrupoMuscular[exercicio.grupoMuscular] = volumeExercicio;
      }
      
      // Atualiza volume total
      volumeTotal += volumeExercicio;
    });

    // Classificação do volume baseada no nível e objetivo
    let classificacaoVolume = '';
    const volumePorSemana = volumeTotal * 2; // Assumindo 2 treinos por grupo muscular por semana

    if (objetivo === 'hipertrofia') {
      if (volumePorSemana < 60000) {
        classificacaoVolume = 'Baixo';
      } else if (volumePorSemana < 100000) {
        classificacaoVolume = 'Moderado';
      } else {
        classificacaoVolume = 'Alto';
      }
    } else if (objetivo === 'força') {
      if (volumePorSemana < 40000) {
        classificacaoVolume = 'Baixo';
      } else if (volumePorSemana < 80000) {
        classificacaoVolume = 'Moderado';
      } else {
        classificacaoVolume = 'Alto';
      }
    } else { // resistencia
      if (volumePorSemana < 80000) {
        classificacaoVolume = 'Baixo';
      } else if (volumePorSemana < 120000) {
        classificacaoVolume = 'Moderado';
      } else {
        classificacaoVolume = 'Alto';
      }
    }

    // Recomendações baseadas na análise
    const recomendacoes: string[] = [
      `Volume total semanal: ${volumePorSemana.toLocaleString()} kg`,
      `Classificação do volume: ${classificacaoVolume}`
    ];

    // Recomendações específicas por nível
    if (nivelTreinamento === 'iniciante') {
      recomendacoes.push(
        'Foque em aprender a técnica correta dos exercícios',
        'Aumente o volume gradualmente nas próximas semanas',
        'Mantenha um diário de treino para acompanhar a progressão'
      );
    } else if (nivelTreinamento === 'intermediario') {
      recomendacoes.push(
        'Considere implementar técnicas avançadas como séries drop',
        'Varie o volume entre as semanas para periodização',
        'Monitore os sinais de recuperação entre treinos'
      );
    } else {
      recomendacoes.push(
        'Implemente microciclos de volume variado',
        'Utilize técnicas avançadas de intensificação',
        'Considere dividir o volume em mais sessões semanais'
      );
    }

    // Recomendações baseadas no volume
    if (classificacaoVolume === 'Baixo') {
      recomendacoes.push(
        'Considere aumentar gradualmente o volume nas próximas semanas',
        'Aumente primeiro o número de séries, depois as repetições'
      );
    } else if (classificacaoVolume === 'Alto') {
      recomendacoes.push(
        'Monitore sinais de fadiga e recuperação',
        'Considere implementar semanas de deload',
        'Garanta nutrição e descanso adequados'
      );
    }

    return {
      volumeTotal,
      volumePorGrupoMuscular,
      volumePorExercicio,
      mediaCargaPorExercicio,
      classificacaoVolume,
      recomendacoes
    };
  };

  const calcularProgressaoCargas = (
    historicoExercicios: Array<{
      nome: string;
      carga: number;
      data: Date;
      nivelTreinamento: 'iniciante' | 'intermediario' | 'avancado';
      objetivo: 'hipertrofia' | 'força' | 'resistencia';
    }>
  ): ProgressaoAnalise => {
    const progressaoPorExercicio: ProgressaoAnalise['progressaoPorExercicio'] = {};
    const exerciciosEstagnados: string[] = [];
    const exerciciosRapidos: string[] = [];
    const exerciciosLentos: string[] = [];
    
    // Agrupa exercícios por nome
    const exerciciosAgrupados = historicoExercicios.reduce((acc, exercicio) => {
      if (!acc[exercicio.nome]) {
        acc[exercicio.nome] = [];
      }
      acc[exercicio.nome].push(exercicio);
      return acc;
    }, {} as { [key: string]: typeof historicoExercicios });

    // Analisa progressão para cada exercício
    Object.entries(exerciciosAgrupados).forEach(([nome, historico]) => {
      const historicoOrdenado = historico.sort((a, b) => a.data.getTime() - b.data.getTime());
      const cargaInicial = historicoOrdenado[0].carga;
      const cargaAtual = historicoOrdenado[historicoOrdenado.length - 1].carga;
      const semanasTreino = Math.ceil(
        (historicoOrdenado[historicoOrdenado.length - 1].data.getTime() - 
         historicoOrdenado[0].data.getTime()) / (7 * 24 * 60 * 60 * 1000)
      );

      const progressaoTotal = ((cargaAtual - cargaInicial) / cargaInicial) * 100;
      const progressaoSemanal = progressaoTotal / semanasTreino;

      // Define progressão ideal baseada no nível e objetivo
      let progressaoIdeal = 0;
      const ultimoRegistro = historicoOrdenado[historicoOrdenado.length - 1];
      
      if (ultimoRegistro.objetivo === 'força') {
        switch (ultimoRegistro.nivelTreinamento) {
          case 'iniciante':
            progressaoIdeal = 2.5; // 2.5% por semana
            break;
          case 'intermediario':
            progressaoIdeal = 1.5; // 1.5% por semana
            break;
          case 'avancado':
            progressaoIdeal = 0.5; // 0.5% por semana
            break;
        }
      } else if (ultimoRegistro.objetivo === 'hipertrofia') {
        switch (ultimoRegistro.nivelTreinamento) {
          case 'iniciante':
            progressaoIdeal = 2.0; // 2.0% por semana
            break;
          case 'intermediario':
            progressaoIdeal = 1.0; // 1.0% por semana
            break;
          case 'avancado':
            progressaoIdeal = 0.3; // 0.3% por semana
            break;
        }
      } else { // resistencia
        switch (ultimoRegistro.nivelTreinamento) {
          case 'iniciante':
            progressaoIdeal = 1.5; // 1.5% por semana
            break;
          case 'intermediario':
            progressaoIdeal = 0.8; // 0.8% por semana
            break;
          case 'avancado':
            progressaoIdeal = 0.2; // 0.2% por semana
            break;
        }
      }

      // Determina status da progressão
      let status: 'abaixo' | 'adequada' | 'acima' = 'adequada';
      if (progressaoSemanal < progressaoIdeal * 0.7) {
        status = 'abaixo';
        exerciciosLentos.push(nome);
      } else if (progressaoSemanal > progressaoIdeal * 1.3) {
        status = 'acima';
        exerciciosRapidos.push(nome);
      }

      // Identifica exercícios estagnados (sem progressão nas últimas 3 semanas)
      const ultimasTresSemanas = historicoOrdenado.slice(-3);
      if (ultimasTresSemanas.length >= 3 && 
          ultimasTresSemanas.every(ex => ex.carga === ultimasTresSemanas[0].carga)) {
        exerciciosEstagnados.push(nome);
      }

      progressaoPorExercicio[nome] = {
        cargaInicial,
        cargaAtual,
        progressaoTotal,
        progressaoSemanal,
        progressaoIdeal,
        status
      };
    });

    // Calcula média geral de progressão
    const mediaProgressao = Object.values(progressaoPorExercicio)
      .reduce((acc, curr) => acc + curr.progressaoSemanal, 0) / 
      Object.keys(progressaoPorExercicio).length;

    // Gera recomendações
    const recomendacoes: string[] = [
      `Progressão média semanal: ${mediaProgressao.toFixed(1)}%`
    ];

    if (exerciciosEstagnados.length > 0) {
      recomendacoes.push(
        `Exercícios estagnados: ${exerciciosEstagnados.join(', ')}`,
        'Considere técnicas de plateau breaking:',
        '- Alteração no número de repetições',
        '- Inclusão de séries drop',
        '- Modificação na cadência do movimento'
      );
    }

    if (exerciciosRapidos.length > 0) {
      recomendacoes.push(
        `Exercícios com progressão acelerada: ${exerciciosRapidos.join(', ')}`,
        'Monitore a técnica de execução',
        'Considere reduzir os incrementos de carga'
      );
    }

    if (exerciciosLentos.length > 0) {
      recomendacoes.push(
        `Exercícios com progressão lenta: ${exerciciosLentos.join(', ')}`,
        'Verifique possíveis causas:',
        '- Técnica de execução',
        '- Recuperação entre séries',
        '- Nutrição e descanso adequados'
      );
    }

    return {
      progressaoPorExercicio,
      progressaoGeral: {
        mediaProgressao,
        exerciciosEstagnados,
        exerciciosRapidos,
        exerciciosLentos
      },
      recomendacoes
    };
  };

  const calcularPeriodizacao = (
    nivelTreinamento: 'iniciante' | 'intermediario' | 'avancado',
    objetivo: 'hipertrofia' | 'força' | 'resistencia',
    historicoTreino: Array<{
      exercicio: string;
      grupoMuscular: string;
      series: number;
      repeticoes: number;
      carga: number;
      data: Date;
    }>,
    semanaAtual: number
  ): PeriodizacaoAnalise => {
    // Definir fase atual baseado no objetivo e nível
    let fase: PeriodizacaoAnalise['fase'] = 'adaptacao';
    let duracaoFase = 4; // Duração padrão em semanas

    // Determinar fase atual baseado no objetivo e progresso
    if (semanaAtual <= 4) {
      fase = 'adaptacao';
      duracaoFase = 4;
    } else if (semanaAtual <= 12) {
      fase = objetivo === 'hipertrofia' ? 'hipertrofia' : 
             objetivo === 'força' ? 'forca' : 'resistencia';
      duracaoFase = 8;
    } else if (semanaAtual <= 16) {
      fase = 'pico';
      duracaoFase = 4;
    } else {
      fase = 'transicao';
      duracaoFase = 2;
    }

    // Calcular volume semanal baseado na fase
    const volumeSemanal = {
      series: fase === 'adaptacao' ? 12 :
              fase === 'hipertrofia' ? 16 :
              fase === 'forca' ? 20 :
              fase === 'resistencia' ? 15 :
              fase === 'pico' ? 24 : 10,
      repeticoes: fase === 'adaptacao' ? 12 :
                  fase === 'hipertrofia' ? 10 :
                  fase === 'forca' ? 6 :
                  fase === 'resistencia' ? 15 :
                  fase === 'pico' ? 4 : 12,
      intensidade: fase === 'adaptacao' ? 65 :
                  fase === 'hipertrofia' ? 75 :
                  fase === 'forca' ? 85 :
                  fase === 'resistencia' ? 60 :
                  fase === 'pico' ? 90 : 50
    };

    // Agrupar exercícios por grupo muscular
    const gruposMusculares = historicoTreino.reduce((acc, treino) => {
      if (!acc[treino.grupoMuscular]) {
        acc[treino.grupoMuscular] = {
          frequenciaSemanal: 0,
          volumeSemanal: 0,
          exerciciosPrincipais: []
        };
      }
      
      acc[treino.grupoMuscular].volumeSemanal += treino.series * treino.repeticoes * treino.carga;
      if (!acc[treino.grupoMuscular].exerciciosPrincipais.includes(treino.exercicio)) {
        acc[treino.grupoMuscular].exerciciosPrincipais.push(treino.exercicio);
      }
      acc[treino.grupoMuscular].frequenciaSemanal = Math.ceil(acc[treino.grupoMuscular].exerciciosPrincipais.length / 3);
      
      return acc;
    }, {} as PeriodizacaoAnalise['distribuicaoGruposMusculares']);

    // Definir parâmetros de treino baseado na fase
    const parametrosTreino = {
      tempoRecuperacao: fase === 'adaptacao' ? 90 :
                        fase === 'hipertrofia' ? 60 :
                        fase === 'forca' ? 180 :
                        fase === 'resistencia' ? 45 :
                        fase === 'pico' ? 240 : 120,
      intensidadeMedia: volumeSemanal.intensidade,
      densidadeTreino: fase === 'adaptacao' ? 0.8 :
                      fase === 'hipertrofia' ? 0.7 :
                      fase === 'forca' ? 0.5 :
                      fase === 'resistencia' ? 0.9 :
                      fase === 'pico' ? 0.4 : 0.6,
      metodosIntensificacao: fase === 'adaptacao' ? ['Pirâmide Crescente'] :
                            fase === 'hipertrofia' ? ['Drop-set', 'Super-série', 'Bi-set'] :
                            fase === 'forca' ? ['Cluster-set', 'Rest-pause'] :
                            fase === 'resistencia' ? ['Circuito', 'HIIT'] :
                            fase === 'pico' ? ['Wave-loading', 'Cluster-set'] : ['Circuito Leve']
    };

    // Gerar recomendações baseadas na fase atual
    const recomendacoes = [
      `Fase atual: ${fase.toUpperCase()} - Duração: ${duracaoFase} semanas`,
      `Volume semanal ideal: ${volumeSemanal.series} séries com ${volumeSemanal.repeticoes} repetições a ${volumeSemanal.intensidade}% 1RM`,
      `Tempo de recuperação entre séries: ${parametrosTreino.tempoRecuperacao} segundos`,
      `Métodos de intensificação recomendados: ${parametrosTreino.metodosIntensificacao.join(', ')}`,
      `Densidade do treino: ${parametrosTreino.densidadeTreino * 100}% (razão trabalho/descanso)`,
      ...Object.entries(gruposMusculares).map(([grupo, dados]) => 
        `${grupo}: ${dados.frequenciaSemanal}x/semana, volume semanal: ${dados.volumeSemanal.toFixed(0)} kg`
      )
    ];

    return {
      fase,
      semanaAtual,
      duracaoFase,
      volumeSemanal,
      distribuicaoGruposMusculares: gruposMusculares,
      parametrosTreino,
      recomendacoes
    };
  };

  const calcularHistoricoEvolucao = (
    historicoCompleto: Array<{
      data: Date;
      exercicio: string;
      grupoMuscular: string;
      series: number;
      repeticoes: number;
      carga: number;
      tempoTreino: number;
      observacoes?: string;
    }>
  ): HistoricoEvolucaoAnalise => {
    // Ordenar histórico por data
    const historicoOrdenado = [...historicoCompleto].sort((a, b) => a.data.getTime() - b.data.getTime());
    const dataInicial = historicoOrdenado[0].data;
    const dataFinal = historicoOrdenado[historicoOrdenado.length - 1].data;
    const periodoTotal = Math.ceil((dataFinal.getTime() - dataInicial.getTime()) / (7 * 24 * 60 * 60 * 1000));

    // Calcular evolução geral
    const evolucaoGeral = {
      periodoTotal,
      volumeAcumulado: historicoOrdenado.reduce((acc, treino) => 
        acc + (treino.series * treino.repeticoes * treino.carga), 0),
      progressaoMedia: 0, // Será calculado depois
      tempoTotalTreino: historicoOrdenado.reduce((acc, treino) => acc + treino.tempoTreino, 0)
    };

    // Agrupar por grupo muscular
    const gruposMusculares = historicoOrdenado.reduce((acc, treino) => {
      if (!acc[treino.grupoMuscular]) {
        acc[treino.grupoMuscular] = {
          volumeInicial: 0,
          volumeAtual: 0,
          progressaoTotal: 0,
          exerciciosMaisEvoluidos: [],
          exerciciosMenosEvoluidos: []
        };
      }
      return acc;
    }, {} as HistoricoEvolucaoAnalise['evolucaoPorGrupoMuscular']);

    // Calcular evolução por grupo muscular
    Object.keys(gruposMusculares).forEach(grupo => {
      const treinosGrupo = historicoOrdenado.filter(t => t.grupoMuscular === grupo);
      const primeirosTreinos = treinosGrupo.slice(0, 4); // Primeiras 4 sessões
      const ultimosTreinos = treinosGrupo.slice(-4); // Últimas 4 sessões

      const volumeInicial = primeirosTreinos.reduce((acc, treino) => 
        acc + (treino.series * treino.repeticoes * treino.carga), 0) / primeirosTreinos.length;
      
      const volumeAtual = ultimosTreinos.reduce((acc, treino) => 
        acc + (treino.series * treino.repeticoes * treino.carga), 0) / ultimosTreinos.length;

      gruposMusculares[grupo] = {
        volumeInicial,
        volumeAtual,
        progressaoTotal: ((volumeAtual - volumeInicial) / volumeInicial) * 100,
        exerciciosMaisEvoluidos: [],
        exerciciosMenosEvoluidos: []
      };
    });

    // Identificar marcos importantes
    const marcosImportantes = historicoOrdenado.reduce((marcos, treino, index, array) => {
      const volumeTreino = treino.series * treino.repeticoes * treino.carga;
      
      // Verificar PRs (Personal Records)
      if (index > 0) {
        const treinosAnteriores = array.slice(0, index).filter(t => t.exercicio === treino.exercicio);
        const maxAnterior = Math.max(...treinosAnteriores.map(t => t.carga));
        if (treino.carga > maxAnterior) {
          marcos.push({
            data: treino.data,
            tipo: 'PR',
            descricao: `Novo recorde de carga em ${treino.exercicio}: ${treino.carga}kg`,
            exercicio: treino.exercicio,
            valor: treino.carga
          });
        }
      }

      // Verificar volumes significativos
      const mediaVolume = array.reduce((acc, t) => acc + (t.series * t.repeticoes * t.carga), 0) / array.length;
      if (volumeTreino > mediaVolume * 1.5) {
        marcos.push({
          data: treino.data,
          tipo: 'Volume',
          descricao: `Volume excepcional em ${treino.exercicio}`,
          exercicio: treino.exercicio,
          valor: volumeTreino
        });
      }

      // Adicionar marcos de técnica/lesão/recuperação baseados em observações
      if (treino.observacoes) {
        if (treino.observacoes.toLowerCase().includes('técnica')) {
          marcos.push({
            data: treino.data,
            tipo: 'Técnica',
            descricao: treino.observacoes,
            exercicio: treino.exercicio
          });
        }
        if (treino.observacoes.toLowerCase().includes('lesão')) {
          marcos.push({
            data: treino.data,
            tipo: 'Lesão',
            descricao: treino.observacoes,
            exercicio: treino.exercicio
          });
        }
        if (treino.observacoes.toLowerCase().includes('recuperação')) {
          marcos.push({
            data: treino.data,
            tipo: 'Recuperação',
            descricao: treino.observacoes,
            exercicio: treino.exercicio
          });
        }
      }

      return marcos;
    }, [] as HistoricoEvolucaoAnalise['marcosImportantes']);

    // Calcular indicadores de desempenho
    const treinosPorSemana = historicoOrdenado.length / periodoTotal;
    const treinosPrevistos = periodoTotal * 3; // Assumindo ideal de 3 treinos por semana
    const indicadoresDesempenho = {
      consistencia: (historicoOrdenado.length / treinosPrevistos) * 100,
      intensidadeMedia: historicoOrdenado.reduce((acc, treino) => acc + treino.carga, 0) / historicoOrdenado.length,
      volumeMedioPorSemana: evolucaoGeral.volumeAcumulado / periodoTotal,
      tempoMedioPorTreino: evolucaoGeral.tempoTotalTreino / historicoOrdenado.length
    };

    // Analisar tendências
    const analisarTendencia = (valores: number[]): 'crescente' | 'estavel' | 'decrescente' => {
      const mediaInicial = valores.slice(0, Math.floor(valores.length / 3)).reduce((a, b) => a + b, 0) / Math.floor(valores.length / 3);
      const mediaFinal = valores.slice(-Math.floor(valores.length / 3)).reduce((a, b) => a + b, 0) / Math.floor(valores.length / 3);
      const diferenca = ((mediaFinal - mediaInicial) / mediaInicial) * 100;
      
      if (diferenca > 5) return 'crescente';
      if (diferenca < -5) return 'decrescente';
      return 'estavel';
    };

    const volumesPorSemana = Array.from({length: periodoTotal}, (_, i) => {
      const treinosSemana = historicoOrdenado.filter(treino => {
        const semanaTreino = Math.floor((treino.data.getTime() - dataInicial.getTime()) / (7 * 24 * 60 * 60 * 1000));
        return semanaTreino === i;
      });
      return treinosSemana.reduce((acc, treino) => acc + (treino.series * treino.repeticoes * treino.carga), 0);
    });

    const intensidadesPorSemana = Array.from({length: periodoTotal}, (_, i) => {
      const treinosSemana = historicoOrdenado.filter(treino => {
        const semanaTreino = Math.floor((treino.data.getTime() - dataInicial.getTime()) / (7 * 24 * 60 * 60 * 1000));
        return semanaTreino === i;
      });
      return treinosSemana.reduce((acc, treino) => acc + treino.carga, 0) / (treinosSemana.length || 1);
    });

    const frequenciasPorSemana = Array.from({length: periodoTotal}, (_, i) => {
      return historicoOrdenado.filter(treino => {
        const semanaTreino = Math.floor((treino.data.getTime() - dataInicial.getTime()) / (7 * 24 * 60 * 60 * 1000));
        return semanaTreino === i;
      }).length;
    });

    const tendencias = {
      volumeProgressao: analisarTendencia(volumesPorSemana),
      intensidadeProgressao: analisarTendencia(intensidadesPorSemana),
      frequenciaProgressao: analisarTendencia(frequenciasPorSemana)
    };

    // Gerar recomendações baseadas na análise
    const recomendacoes = [
      `Período total de treino: ${periodoTotal} semanas`,
      `Média de ${treinosPorSemana.toFixed(1)} treinos por semana`,
      `Consistência de treino: ${indicadoresDesempenho.consistencia.toFixed(1)}%`,
      `Volume médio semanal: ${indicadoresDesempenho.volumeMedioPorSemana.toFixed(0)} kg`,
      `Duração média dos treinos: ${(indicadoresDesempenho.tempoMedioPorTreino / 60).toFixed(1)} horas`,
      `Tendência de volume: ${tendencias.volumeProgressao}`,
      `Tendência de intensidade: ${tendencias.intensidadeProgressao}`,
      `Tendência de frequência: ${tendencias.frequenciaProgressao}`,
      ...marcosImportantes.map(marco => `${marco.data.toLocaleDateString()}: ${marco.descricao}`)
    ];

    // Atualizar progressão média na evolução geral
    evolucaoGeral.progressaoMedia = Object.values(gruposMusculares)
      .reduce((acc, grupo) => acc + grupo.progressaoTotal, 0) / Object.keys(gruposMusculares).length;

    return {
      evolucaoGeral,
      evolucaoPorGrupoMuscular: gruposMusculares,
      marcosImportantes,
      indicadoresDesempenho,
      tendencias,
      recomendacoes
    };
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    try {
      const novaAvaliacao: AvaliacaoFisica = {
        id: avaliacaoParaEditar?.id,
        cliente_id: avaliacao.cliente_id,
        data_avaliacao: avaliacao.data_avaliacao,
        peso: avaliacao.peso,
        altura: avaliacao.altura,
        idade: avaliacao.idade,
        nivelAtividade: avaliacao.nivelAtividade,
        intensidadeExercicios: avaliacao.intensidadeExercicios,
        frequenciaSemanal: avaliacao.frequenciaSemanal,
        tipoTrabalho: avaliacao.tipoTrabalho,
        objetivo: avaliacao.objetivo,
        percentualGordura: avaliacao.percentualGordura,
        dobras_cutaneas: avaliacao.dobras_cutaneas,
        medidas_antropometricas: avaliacao.medidas_antropometricas
      };

      if (avaliacaoParaEditar?.id) {
        await atualizarAvaliacao(avaliacaoParaEditar.id, novaAvaliacao);
        setMensagem('Avaliação física atualizada com sucesso!');
      } else {
        await criarAvaliacao(novaAvaliacao);
        setMensagem('Avaliação física criada com sucesso!');
      }
      setTipoMensagem('success');
      setMostrarMensagem(true);
      if (onSalvar) onSalvar();
    } catch (error) {
      console.error('Erro ao salvar avaliação:', error);
      setMensagem('Erro ao salvar avaliação física');
      setTipoMensagem('error');
      setMostrarMensagem(true);
    }
  };

  const handleMetasNutricionais = () => {
    const metasNutricionais = calcularMetasNutricionais(
      avaliacao.peso,
      avaliacao.altura.toString(),
      avaliacao.objetivo,
      avaliacao.percentualGordura
    );
    // ... rest of the function
  };

  if (carregando) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress sx={{ color: '#0038FF' }} />
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Paper 
        elevation={3} 
        sx={{ 
          borderRadius: 4,
          overflow: 'hidden'
        }}
      >
        <Box sx={{ bgcolor: '#000000', color: 'white', p: 2 }}>
          <Typography variant="h5" fontWeight="bold">
            {avaliacaoParaEditar ? 'Editar Avaliação Física' : 'Nova Avaliação Física'}
        </Typography>
        </Box>
        
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
          {/* Dados Básicos */}
          <Grid item xs={12}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <PersonIcon sx={{ color: '#0038FF' }} />
                <Typography variant="h6" fontWeight="bold">
              Dados Básicos
            </Typography>
              </Stack>
              <Divider sx={{ mt: 1, mb: 2 }} />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Cliente</InputLabel>
              <Select
                name="cliente_id"
                value={avaliacao.cliente_id}
                onChange={(e) => handleChange(e, 'avaliacao')}
                disabled={!!clienteId}
                  sx={{
                    borderRadius: 2,
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#0038FF',
                    },
                  }}
              >
                {clientes.map((cliente) => (
                  <MenuItem key={cliente.id} value={cliente.id}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Avatar 
                          sx={{ 
                            width: 24, 
                            height: 24, 
                            bgcolor: '#0038FF',
                            fontSize: '0.8rem'
                          }}
                        >
                          {cliente.nome?.charAt(0).toUpperCase()}
                        </Avatar>
                        <Typography>{cliente.nome}</Typography>
                      </Stack>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Data da Avaliação"
              name="data_avaliacao"
              type="date"
              value={avaliacao.data_avaliacao}
              onChange={(e) => handleChange(e, 'avaliacao')}
              InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Peso (kg)"
              name="peso"
              type="number"
              value={avaliacao.peso}
              onChange={(e) => handleChange(e, 'avaliacao')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
                label="Altura (cm)"
              name="altura"
              type="number"
              value={avaliacao.altura}
              onChange={(e) => handleChange(e, 'avaliacao')}
                inputProps={{ step: 1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Idade"
              name="idade"
              type="number"
              value={avaliacao.idade}
              onChange={(e) => handleChange(e, 'avaliacao')}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          {/* Dobras Cutâneas */}
          <Grid item xs={12}>
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 2 }}>
                <MonitorWeightIcon sx={{ color: '#0038FF' }} />
                <Typography variant="h6" fontWeight="bold">
                  Dobras Cutâneas
            </Typography>
                <Tooltip title="Medidas em milímetros (mm)">
                  <IconButton size="small">
                    <HelpOutlineIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
              <Divider sx={{ mt: 1, mb: 2 }} />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
                label="Peitoral"
                name="peitoral"
              type="number"
                value={avaliacao.dobras_cutaneas?.peitoral || ''}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Tricipital"
              name="tricipital"
              type="number"
              value={avaliacao.dobras_cutaneas?.tricipital || ''}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Bicipital"
              name="bicipital"
              type="number"
              value={avaliacao.dobras_cutaneas.bicipital}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Axilar Média"
              name="axilar_media"
              type="number"
              value={avaliacao.dobras_cutaneas.axilar_media}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Suprailíaca"
              name="suprailiaca"
              type="number"
              value={avaliacao.dobras_cutaneas.suprailiaca}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Abdominal"
              name="abdominal"
              type="number"
              value={avaliacao.dobras_cutaneas.abdominal}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Coxa"
              name="coxa"
              type="number"
              value={avaliacao.dobras_cutaneas.coxa}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Panturrilha"
              name="panturrilha"
              type="number"
              value={avaliacao.dobras_cutaneas.panturrilha}
              onChange={(e) => handleChange(e, 'dobras')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  borderRadius: 3,
                  bgcolor: '#f8f9fa',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2
                }}
              >
                <Tooltip title="Calcula o percentual de gordura usando a fórmula de Jackson & Pollock (7 dobras) e a equação de Siri. O cálculo considera idade, sexo e a soma das dobras: peitoral, tricipital, axilar média, suprailíaca, abdominal, coxa e panturrilha." arrow>
              <Button
                    variant="contained"
                onClick={calcularPercentualGordura}
                    sx={{
                      bgcolor: '#0038FF',
                      borderRadius: 2,
                      '&:hover': {
                        bgcolor: '#0026B3',
                      },
                    }}
              >
                Calcular % de Gordura
              </Button>
                </Tooltip>
              <TextField
                label="Percentual de Gordura"
                name="percentual_gordura"
                type="number"
                value={avaliacao.dobras_cutaneas.percentual_gordura}
                onChange={(e) => handleChange(e, 'dobras')}
                inputProps={{ step: 0.1 }}
                  sx={{
                    flexGrow: 1,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '& fieldset': {
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#0038FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#0038FF',
                      },
                    },
                  }}
                />
              </Paper>
          </Grid>

          {/* Medidas Antropométricas */}
          <Grid item xs={12}>
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 2 }}>
                <StraightenIcon sx={{ color: '#0038FF' }} />
                <Typography variant="h6" fontWeight="bold">
                  Medidas Antropométricas
            </Typography>
                <Tooltip title="Medidas em centímetros (cm)">
                  <IconButton size="small">
                    <HelpOutlineIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
              <Divider sx={{ mt: 1, mb: 2 }} />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Braço Direito"
              name="braco_direito"
              type="number"
              value={avaliacao.medidas_antropometricas.braco_direito}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Braço Esquerdo"
              name="braco_esquerdo"
              type="number"
              value={avaliacao.medidas_antropometricas.braco_esquerdo}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Antebraço Direito"
              name="antebraco_direito"
              type="number"
              value={avaliacao.medidas_antropometricas.antebraco_direito}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Antebraço Esquerdo"
              name="antebraco_esquerdo"
              type="number"
              value={avaliacao.medidas_antropometricas.antebraco_esquerdo}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
                label="Peitoral"
                name="peitoral"
              type="number"
                value={avaliacao.medidas_antropometricas.peitoral}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Cintura"
              name="cintura"
              type="number"
              value={avaliacao.medidas_antropometricas.cintura}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Abdômen"
              name="abdomen"
              type="number"
              value={avaliacao.medidas_antropometricas.abdomen}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Quadril"
              name="quadril"
              type="number"
              value={avaliacao.medidas_antropometricas.quadril}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Coxa Direita"
              name="coxa_direita"
              type="number"
              value={avaliacao.medidas_antropometricas.coxa_direita}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Coxa Esquerda"
              name="coxa_esquerda"
              type="number"
              value={avaliacao.medidas_antropometricas.coxa_esquerda}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Panturrilha Direita"
              name="panturrilha_direita"
              type="number"
              value={avaliacao.medidas_antropometricas.panturrilha_direita}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Panturrilha Esquerda"
              name="panturrilha_esquerda"
              type="number"
              value={avaliacao.medidas_antropometricas.panturrilha_esquerda}
              onChange={(e) => handleChange(e, 'medidas')}
              inputProps={{ step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.1)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#0038FF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#0038FF',
                    },
                  },
                }}
            />
          </Grid>

            {/* Análise do Biotipo */}
            <Grid item xs={12}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  borderRadius: 3,
                  bgcolor: '#f8f9fa',
                  mt: 2
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MonitorHeartIcon sx={{ color: '#0038FF' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Análise do Biotipo
                  </Typography>
                  <Tooltip title="Classificação baseada em medidas antropométricas, estrutura corporal e composição" arrow>
                    <IconButton size="small">
                      <HelpOutlineIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
                
                {avaliacao.peso && avaliacao.altura && avaliacao.dobras_cutaneas.percentual_gordura && avaliacao.medidas_antropometricas.braco_direito && (
                  <>
                    {(() => {
                      const clienteSelecionado = clientes.find(c => c.id === avaliacao.cliente_id);
                      const biotipo = analisarBiotipo(
                        avaliacao.altura,
                        avaliacao.peso,
                        avaliacao.dobras_cutaneas.percentual_gordura,
                        avaliacao.medidas_antropometricas.braco_direito,
                        clienteSelecionado?.sexo || 'M'
                      );
                      
                      return (
                        <Box sx={{ mt: 2 }}>
                          <Stack direction="row" spacing={2} alignItems="center">
                            <Chip
                              icon={<FitnessCenterIcon />}
                              label={biotipo.tipo}
                              sx={{
                                bgcolor: '#0038FF',
                                color: 'white',
                                fontWeight: 'bold',
                                '& .MuiChip-icon': {
                                  color: 'white'
                                }
                              }}
                            />
                          </Stack>
                          
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                              Características:
                            </Typography>
                            <List dense>
                              {biotipo.caracteristicas.map((caracteristica, index) => (
                                <ListItem key={index}>
                                  <ListItemIcon>
                                    <CheckCircleIcon sx={{ color: '#0038FF' }} />
                                  </ListItemIcon>
                                  <ListItemText primary={caracteristica} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                          
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                              Recomendações de Treino:
                            </Typography>
                            <List dense>
                              {biotipo.recomendacoes.map((recomendacao, index) => (
                                <ListItem key={index}>
                                  <ListItemIcon>
                                    <ArrowRightIcon sx={{ color: '#0038FF' }} />
                                  </ListItemIcon>
                                  <ListItemText primary={recomendacao} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        </Box>
                      );
                    })()}
                  </>
                )}
              </Paper>
        </Grid>

            {/* Análise da TMB */}
            <Grid item xs={12}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  borderRadius: 3,
                  bgcolor: '#f8f9fa',
                  mt: 2
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <LocalFireDepartmentIcon sx={{ color: '#0038FF' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Taxa Metabólica Basal (TMB)
                  </Typography>
                  <Tooltip title="Quantidade mínima de energia necessária para manter as funções vitais do organismo em repouso" arrow>
                    <IconButton size="small">
                      <HelpOutlineIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
                
                {avaliacao.peso && avaliacao.altura && avaliacao.idade && avaliacao.dobras_cutaneas.percentual_gordura && (
                  <>
                    {(() => {
                      const clienteSelecionado = clientes.find(c => c.id === avaliacao.cliente_id);
                      const tmb = calcularTMB(
                        avaliacao.peso,
                        avaliacao.altura,
                        avaliacao.idade,
                        avaliacao.dobras_cutaneas.percentual_gordura,
                        clienteSelecionado?.sexo || 'M'
                      );
                      
                      return (
                        <Box sx={{ mt: 2 }}>
                          <Stack direction="row" spacing={2} alignItems="center">
                            <Chip
                              icon={<LocalFireDepartmentIcon />}
                              label={`${tmb.tmb} kcal/dia`}
                              sx={{
                                bgcolor: '#0038FF',
                                color: 'white',
                                fontWeight: 'bold',
                                '& .MuiChip-icon': {
                                  color: 'white'
                                }
                              }}
                            />
                            <Chip
                              label={`Classificação: ${tmb.classificacao}`}
                              sx={{
                                bgcolor: tmb.classificacao === 'Alto' ? '#4caf50' :
                                       tmb.classificacao === 'Moderado' ? '#ff9800' : '#f44336',
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Stack>
                          
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                              Recomendações Nutricionais:
                            </Typography>
                            <List dense>
                              {tmb.recomendacoes.map((recomendacao, index) => (
                                <ListItem key={index}>
                                  <ListItemIcon>
                                    <ArrowRightIcon sx={{ color: '#0038FF' }} />
                                  </ListItemIcon>
                                  <ListItemText primary={recomendacao} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        </Box>
                      );
                    })()}
                  </>
                )}
              </Paper>
            </Grid>

            {/* Análise do GET */}
            <Grid item xs={12}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  borderRadius: 3,
                  bgcolor: '#f8f9fa',
                  mt: 2
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <LocalFireDepartmentIcon sx={{ color: '#0038FF' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Gasto Energético Total (GET)
                  </Typography>
                  <Tooltip title="Estimativa do gasto calórico diário total baseado na TMB e nível de atividade física" arrow>
                    <IconButton size="small">
                      <HelpOutlineIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>

                <FormControl fullWidth sx={{ mt: 2 }}>
                  <InputLabel>Nível de Atividade Física</InputLabel>
                  <Select
                    value={avaliacao.nivelAtividade}
                    onChange={(e) => handleChange(e, 'nivelAtividade')}
                    sx={{
                      borderRadius: 2,
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#0038FF',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#0038FF',
                      },
                    }}
                  >
                    <MenuItem value="Sedentário">Sedentário (pouco ou nenhum exercício)</MenuItem>
                    <MenuItem value="Levemente Ativo">Levemente Ativo (exercício leve 1-3x/semana)</MenuItem>
                    <MenuItem value="Moderadamente Ativo">Moderadamente Ativo (exercício moderado 3-5x/semana)</MenuItem>
                    <MenuItem value="Muito Ativo">Muito Ativo (exercício pesado 6-7x/semana)</MenuItem>
                    <MenuItem value="Extremamente Ativo">Extremamente Ativo (exercício muito pesado, trabalho físico)</MenuItem>
                  </Select>
                </FormControl>
                
                {avaliacao.peso && avaliacao.altura && avaliacao.idade && avaliacao.dobras_cutaneas.percentual_gordura && (
                  <>
                    {(() => {
                      const clienteSelecionado = clientes.find(c => c.id === avaliacao.cliente_id);
                      const tmb = calcularTMB(
                        avaliacao.peso,
                        avaliacao.altura,
                        avaliacao.idade,
                        avaliacao.dobras_cutaneas.percentual_gordura,
                        clienteSelecionado?.sexo || 'M'
                      );
                      
                      const analiseGET = calcularGET(
                        tmb.tmb,
                        avaliacao.nivelAtividade,
                        clienteSelecionado?.sexo || 'M'
                      );
                      
                      return (
                        <Box sx={{ mt: 2 }}>
                          <Stack direction="row" spacing={2} alignItems="center">
                            <Chip
                              icon={<LocalFireDepartmentIcon />}
                              label={`${analiseGET.get} kcal/dia`}
                              sx={{
                                bgcolor: '#0038FF',
                                color: 'white',
                                fontWeight: 'bold',
                                '& .MuiChip-icon': {
                                  color: 'white'
                                }
                              }}
                            />
                            <Chip
                              label={`Classificação: ${analiseGET.classificacao}`}
                              sx={{
                                bgcolor: analiseGET.classificacao === 'Alto' ? '#4caf50' :
                                       analiseGET.classificacao === 'Moderado' ? '#ff9800' : '#f44336',
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Stack>
                          
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                              Recomendações baseadas no seu nível de atividade:
                            </Typography>
                            <List dense>
                              {analiseGET.recomendacoes.map((recomendacao, index) => (
                                <ListItem key={index}>
                                  <ListItemIcon>
                                    <ArrowRightIcon sx={{ color: '#0038FF' }} />
                                  </ListItemIcon>
                                  <ListItemText primary={recomendacao} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        </Box>
                      );
                    })()}
                  </>
                )}
              </Paper>
            </Grid>

            {/* Análise do Ajuste de Atividade */}
            <Grid item xs={12}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  borderRadius: 3,
                  bgcolor: '#f8f9fa',
                  mt: 2
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MonitorHeartIcon sx={{ color: '#0038FF' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Ajuste de Atividade
                  </Typography>
                  <Tooltip title="Ajuste baseado na intensidade e frequência dos exercícios" arrow>
                    <IconButton size="small">
                      <HelpOutlineIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
                
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Intensidade dos Exercícios</InputLabel>
                      <Select
                        value={avaliacao.intensidadeExercicios}
                        onChange={(e) => handleChange(e, 'intensidadeExercicios')}
                        sx={{
                          borderRadius: 2,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                          },
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#0038FF',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#0038FF',
                          },
                        }}
                      >
                        <MenuItem value="Leve">Leve (Alongamento, Caminhada)</MenuItem>
                        <MenuItem value="Moderada">Moderada (Musculação Leve, Yoga)</MenuItem>
                        <MenuItem value="Alta">Alta (Musculação Intensa, Corrida)</MenuItem>
                        <MenuItem value="Muito Alta">Muito Alta (HIIT, CrossFit)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Frequência Semanal</InputLabel>
                      <Select
                        value={avaliacao.frequenciaSemanal.toString()}
                        onChange={(e) => handleChange(e, 'frequenciaSemanal')}
                        sx={{
                          borderRadius: 2,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                          },
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#0038FF',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#0038FF',
                          },
                        }}
                      >
                        {[0, 1, 2, 3, 4, 5, 6, 7].map((num) => (
                          <MenuItem key={num} value={num}>{num}x por semana</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Tipo de Trabalho</InputLabel>
                      <Select
                        value={avaliacao.tipoTrabalho}
                        onChange={(e) => handleChange(e, 'tipoTrabalho')}
                        sx={{
                          borderRadius: 2,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                          },
                        }}
                      >
                        <MenuItem value="Sedentário">Sedentário (pouco ou nenhum exercício)</MenuItem>
                        <MenuItem value="Leve">Leve (exercício leve 1-3x/semana)</MenuItem>
                        <MenuItem value="Moderado">Moderado (exercício moderado 3-5x/semana)</MenuItem>
                        <MenuItem value="Intenso">Intenso (exercício pesado 6-7x/semana)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {avaliacao.intensidadeExercicios && avaliacao.frequenciaSemanal && avaliacao.tipoTrabalho && (
                    <>
                      {(() => {
                        const ajusteAtividade = calcularAjusteAtividade(
                          avaliacao.intensidadeExercicios,
                          avaliacao.frequenciaSemanal,
                          avaliacao.tipoTrabalho
                        );
                        
                        return (
                          <Box sx={{ mt: 2 }}>
                            <Stack direction="row" spacing={2} alignItems="center">
                              <Chip
                                icon={<MonitorHeartIcon />}
                                label={`Fator de Ajuste: ${ajusteAtividade.fatorAjuste.toFixed(2)}`}
                                sx={{
                                  bgcolor: '#0038FF',
                                  color: 'white',
                                  fontWeight: 'bold',
                                  '& .MuiChip-icon': {
                                    color: 'white'
                                  }
                                }}
                              />
                            </Stack>
                            
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                                Recomendações:
                              </Typography>
                              <List dense>
                                {ajusteAtividade.recomendacoes.map((recomendacao, index) => (
                                  <ListItem key={index}>
                                    <ListItemIcon>
                                      <ArrowRightIcon sx={{ color: '#0038FF' }} />
                                    </ListItemIcon>
                                    <ListItemText primary={recomendacao} />
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          </Box>
                        );
                      })()}
                    </>
                  )}
                </Grid>
              </Paper>
            </Grid>

            {/* Análise do Metas Nutricionais */}
            <Grid item xs={12}>
              <Paper 
                elevation={1} 
                sx={{ 
                  p: 2, 
                  borderRadius: 3,
                  bgcolor: '#f8f9fa',
                  mt: 2
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MonitorHeartIcon sx={{ color: '#0038FF' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Metas Nutricionais
                  </Typography>
                  <Tooltip title="Ajuste calórico baseado no objetivo e % de gordura" arrow>
                    <IconButton size="small">
                      <HelpOutlineIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
                
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Objetivo</InputLabel>
                      <Select
                        value={avaliacao.objetivo}
                        onChange={(e) => handleChange(e, 'objetivo')}
                        sx={{
                          borderRadius: 2,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                          },
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#0038FF',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#0038FF',
                          },
                        }}
                      >
                        <MenuItem value="manutenção">Manutenção</MenuItem>
                        <MenuItem value="perda">Perda de peso</MenuItem>
                        <MenuItem value="ganho">Ganho de peso</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Percentual de Gordura</InputLabel>
                      <TextField
                        value={avaliacao.percentualGordura}
                        onChange={(e) => handleChange(e, 'percentualGordura')}
                        type="number"
                        inputProps={{ step: 0.1 }}
                      />
                    </FormControl>
                  </Grid>

                  {avaliacao.objetivo && avaliacao.percentualGordura && (
                    <>
                      {(() => {
                        const metasNutricionais = calcularMetasNutricionais(
                          avaliacao.peso,
                          avaliacao.altura.toString(),
                          avaliacao.objetivo,
                          avaliacao.percentualGordura
                        );
                        
                        return (
                          <Box sx={{ mt: 2 }}>
                            <Stack direction="row" spacing={2} alignItems="center">
                              <Chip
                                icon={<MonitorHeartIcon />}
                                label={`Calorias Alvo: ${metasNutricionais.caloriasAlvo} kcal/dia`}
                                sx={{
                                  bgcolor: '#0038FF',
                                  color: 'white',
                                  fontWeight: 'bold',
                                  '& .MuiChip-icon': {
                                    color: 'white'
                                  }
                                }}
                              />
                            </Stack>
                            
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                                Recomendações:
                              </Typography>
                              <List dense>
                                {metasNutricionais.recomendacoes.map((recomendacao, index) => (
                                  <ListItem key={index}>
                                    <ListItemIcon>
                                      <ArrowRightIcon sx={{ color: '#0038FF' }} />
                                    </ListItemIcon>
                                    <ListItemText primary={recomendacao} />
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          </Box>
                        );
                      })()}
                    </>
                  )}
                </Grid>
              </Paper>
            </Grid>
          </Grid>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          {onCancelar && (
              <Button 
                onClick={onCancelar} 
                variant="outlined"
                sx={{
                  borderRadius: 2,
                  borderColor: '#000000',
                  color: '#000000',
                  '&:hover': {
                    borderColor: '#000000',
                    bgcolor: 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
              Cancelar
            </Button>
          )}
            <Button 
              type="submit" 
              variant="contained"
              disabled={carregando}
              sx={{
                bgcolor: '#0038FF',
                borderRadius: 2,
                '&:hover': {
                  bgcolor: '#0026B3',
                },
              }}
            >
              {carregando ? <CircularProgress size={24} color="inherit" /> : 'Salvar'}
          </Button>
          </Box>
        </Box>
      </Paper>

      <Snackbar
        open={mostrarMensagem}
        autoHideDuration={6000}
        onClose={() => setMostrarMensagem(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setMostrarMensagem(false)}
          severity={tipoMensagem}
          variant="filled"
          sx={{ 
            width: '100%',
            borderRadius: 2
          }}
        >
          {mensagem}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AvaliacaoFisicaForm; 