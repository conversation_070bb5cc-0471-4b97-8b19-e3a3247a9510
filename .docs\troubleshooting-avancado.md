# 🔧 Troubleshooting Avançado - Personal Trainer App

## 🚨 Problemas Específicos com NPM

### Por que NPM não funciona?

#### Conflitos de Dependências
```bash
# NPM tenta resolver dependências de forma diferente
# Electron + React + Better-SQLite3 = Conflito complexo
npm ERR! peer dep missing: react@^18.0.0
npm ERR! conflicting peer dependency
```

#### Versões Incompatíveis
- **better-sqlite3**: Precisa compilar código nativo
- **electron**: Versões específicas do Node.js
- **react-scripts**: Dependências rígidas

#### Solução Definitiva: YARN
```bash
# Yarn resolve dependências de forma mais inteligente
yarn install  # ✅ Funciona sempre
```

---

## 🖥️ Instalação por Sistema Operacional

### Windows 10/11

#### Pré-requisitos Windows
```bash
# 1. Instalar Node.js (LTS)
# Baixar de: https://nodejs.org/

# 2. Instalar Yarn
npm install -g yarn

# 3. Instalar Git
# Baixar de: https://git-scm.com/

# 4. (Opcional) Visual Studio Build Tools
# Para compilar dependências nativas
```

#### Comandos Windows
```cmd
# PowerShell ou CMD
git clone https://github.com/seu-usuario/personal-trainer-app.git
cd personal-trainer-app
yarn install
yarn dev

# OU usar o arquivo .bat
start.bat
```

#### Problemas Específicos Windows
```bash
# Erro: "execution policy"
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Erro: "gyp ERR! find VS"
npm install -g windows-build-tools

# Erro: "ENOENT: no such file"
# Executar como Administrador
```

### macOS

#### Pré-requisitos macOS
```bash
# 1. Instalar Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. Instalar Node.js
brew install node

# 3. Instalar Yarn
brew install yarn

# 4. Instalar Git (se não tiver)
brew install git
```

#### Comandos macOS
```bash
git clone https://github.com/seu-usuario/personal-trainer-app.git
cd personal-trainer-app
yarn install
yarn dev

# OU usar o script shell
chmod +x start.sh
./start.sh
```

#### Problemas Específicos macOS
```bash
# Erro: "command not found: yarn"
echo 'export PATH="$PATH:`yarn global bin`"' >> ~/.zshrc
source ~/.zshrc

# Erro: "permission denied"
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules

# Erro: "gyp: No Xcode or CLT version"
xcode-select --install
```

### Linux (Ubuntu/Debian)

#### Pré-requisitos Linux
```bash
# 1. Atualizar sistema
sudo apt update && sudo apt upgrade -y

# 2. Instalar Node.js (via NodeSource)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. Instalar Yarn
curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
sudo apt update && sudo apt install yarn

# 4. Instalar dependências de compilação
sudo apt-get install -y build-essential python3
```

#### Comandos Linux
```bash
git clone https://github.com/seu-usuario/personal-trainer-app.git
cd personal-trainer-app
yarn install
yarn dev

# OU usar o script shell
chmod +x start.sh
./start.sh
```

#### Problemas Específicos Linux
```bash
# Erro: "EACCES: permission denied"
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) ~/.config

# Erro: "node-gyp rebuild failed"
sudo apt-get install -y python3-dev build-essential

# Erro: "Electron sandbox"
# Adicionar --no-sandbox se necessário
```

---

## 🔍 Diagnóstico de Problemas

### Verificação Completa do Sistema
```bash
# 1. Verificar versões
node --version     # Deve ser v16+ 
npm --version      # Qualquer versão recente
yarn --version     # Deve ser 1.22+
git --version      # Qualquer versão

# 2. Verificar permissões
npm config get prefix
yarn config get prefix

# 3. Verificar cache
npm cache verify
yarn cache dir

# 4. Verificar registry
npm config get registry
yarn config get registry
```

### Limpeza Completa
```bash
# 1. Limpar caches
npm cache clean --force
yarn cache clean

# 2. Remover node_modules
rm -rf node_modules/
rm package-lock.json  # Se existir
rm yarn.lock          # Se quiser forçar reinstalação

# 3. Reinstalar
yarn install

# 4. Verificar integridade
yarn check
```

### Logs Detalhados
```bash
# Instalar com logs verbosos
yarn install --verbose

# Executar com debug
DEBUG=* yarn dev

# Logs do Electron
yarn dev --enable-logging
```

---

## 🐛 Erros Comuns e Soluções

### 1. "Module not found: better-sqlite3"
```bash
# Problema: Dependência nativa não compilou
# Solução:
yarn remove better-sqlite3
yarn add better-sqlite3

# Se persistir:
yarn add better-sqlite3 --build-from-source
```

### 2. "Electron failed to install correctly"
```bash
# Problema: Download do Electron falhou
# Solução:
yarn remove electron
yarn add electron --network-timeout 100000

# Alternativa:
ELECTRON_MIRROR=https://npm.taobao.org/mirrors/electron/ yarn add electron
```

### 3. "Port 3000 is already in use"
```bash
# Problema: Porta ocupada
# Solução Windows:
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Solução Linux/Mac:
lsof -ti:3000 | xargs kill -9

# Alternativa: Usar outra porta
PORT=3001 yarn start
```

### 4. "Cannot resolve dependency tree"
```bash
# Problema: Conflito de dependências (NPM)
# Solução: Usar YARN sempre
rm package-lock.json
yarn install
```

### 5. "Python executable not found"
```bash
# Problema: node-gyp precisa do Python
# Windows:
npm install -g windows-build-tools

# Linux:
sudo apt-get install python3-dev

# Mac:
xcode-select --install
```

---

## 🚀 Otimizações de Performance

### Acelerar Instalação
```bash
# Usar cache local
yarn install --prefer-offline

# Instalar em paralelo
yarn install --network-concurrency 16

# Pular scripts opcionais
yarn install --ignore-optional
```

### Reduzir Tamanho
```bash
# Instalar apenas produção
yarn install --production

# Limpar dev dependencies
yarn autoclean --init
yarn autoclean --force
```

### Cache Inteligente
```bash
# Configurar cache global
yarn config set cache-folder ~/.yarn-cache

# Verificar cache
yarn cache list

# Limpar cache seletivo
yarn cache clean <package-name>
```

---

## 📊 Monitoramento e Debug

### Verificar Saúde da Instalação
```bash
# Verificar dependências
yarn list --depth=0

# Verificar vulnerabilidades
yarn audit

# Verificar licenças
yarn licenses list

# Verificar duplicatas
yarn list --pattern "react"
```

### Debug Avançado
```bash
# Logs detalhados do Yarn
yarn install --verbose

# Debug do Node.js
NODE_DEBUG=* yarn dev

# Debug do Electron
yarn dev --inspect=9229

# Profiling
yarn dev --prof
```

---

## ✅ Checklist de Verificação Final

### Antes de Reportar Problemas
- [ ] Node.js versão 16+ instalado
- [ ] Yarn instalado globalmente
- [ ] Permissões corretas nas pastas
- [ ] Cache limpo
- [ ] node_modules removido e reinstalado
- [ ] Testado apenas com yarn (nunca npm)
- [ ] Logs verificados para erros específicos
- [ ] Testado em terminal limpo
- [ ] Antivírus não bloqueando
- [ ] Espaço em disco suficiente (>2GB)

### Comandos de Verificação Rápida
```bash
# Teste completo em 1 comando
yarn install && yarn start
```

Se todos esses passos falharem, o problema pode ser específico do ambiente. Documente o erro exato e o sistema operacional para suporte adicional.
