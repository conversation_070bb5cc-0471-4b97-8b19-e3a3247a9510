import React from 'react';
import { Container } from '../common/Container';
import { Exercicio } from '../../models/Exercicio';

interface ExercicioFormProps {
  onSubmit: (exercicio: Exercicio) => void;
  initialData?: Exercicio;
}

export const ExercicioForm = ({ onSubmit, initialData }: ExercicioFormProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Implementação do handleSubmit
  };

  return (
    <Container
      title={initialData ? "Editar Exercício" : "Novo Exercício"}
      subtitle="Cadastre um novo exercício para usar nos treinos"
      useGradient
    >
      <form onSubmit={handleSubmit}>
        {/* Implementação do formulário */}
      </form>
    </Container>
  );
}; 