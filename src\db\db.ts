import path from 'path';

// Verificar se estamos no Electron
const isElectron = typeof window !== 'undefined' &&
                  window.navigator &&
                  /electron/i.test(window.navigator.userAgent);

// Importação condicional do better-sqlite3 (apenas no Electron)
let Database: any = null;
if (isElectron) {
  try {
    // eslint-disable-next-line
    Database = require('better-sqlite3');
  } catch (error) {
    console.error('Erro ao importar better-sqlite3:', error);
  }
}

const isDevelopment = process.env.NODE_ENV === 'development';
const dbPath = isDevelopment ? './database.sqlite' : path.join(process.cwd(), 'database.sqlite');

// Implementação de banco de dados para navegador usando IndexedDB
class BrowserDatabase {
  private dbName = 'PersonalTrainerDB';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Criar tabelas (object stores)
        if (!db.objectStoreNames.contains('clientes')) {
          const clientesStore = db.createObjectStore('clientes', { keyPath: 'id', autoIncrement: true });
          clientesStore.createIndex('nome', 'nome', { unique: false });
        }

        if (!db.objectStoreNames.contains('avaliacoes_fisicas')) {
          const avaliacoesStore = db.createObjectStore('avaliacoes_fisicas', { keyPath: 'id', autoIncrement: true });
          avaliacoesStore.createIndex('cliente_id', 'cliente_id', { unique: false });
        }

        if (!db.objectStoreNames.contains('dobras_cutaneas')) {
          const dobrasStore = db.createObjectStore('dobras_cutaneas', { keyPath: 'id', autoIncrement: true });
          dobrasStore.createIndex('avaliacao_id', 'avaliacao_id', { unique: false });
        }

        if (!db.objectStoreNames.contains('medidas_antropometricas')) {
          const medidasStore = db.createObjectStore('medidas_antropometricas', { keyPath: 'id', autoIncrement: true });
          medidasStore.createIndex('avaliacao_id', 'avaliacao_id', { unique: false });
        }

        if (!db.objectStoreNames.contains('treinos')) {
          const treinosStore = db.createObjectStore('treinos', { keyPath: 'id', autoIncrement: true });
          treinosStore.createIndex('cliente_id', 'cliente_id', { unique: false });
        }

        if (!db.objectStoreNames.contains('exercicios')) {
          const exerciciosStore = db.createObjectStore('exercicios', { keyPath: 'id', autoIncrement: true });
          exerciciosStore.createIndex('treino_id', 'treino_id', { unique: false });
        }

        if (!db.objectStoreNames.contains('series')) {
          const seriesStore = db.createObjectStore('series', { keyPath: 'id', autoIncrement: true });
          seriesStore.createIndex('exercicio_id', 'exercicio_id', { unique: false });
        }
      };
    });
  }

  prepare(sql: string) {
    // Simular interface do SQLite para compatibilidade
    return {
      run: (...params: any[]) => this.executeQuery(sql, params, 'run'),
      get: (...params: any[]) => this.executeQuery(sql, params, 'get'),
      all: (...params: any[]) => this.executeQuery(sql, params, 'all')
    };
  }

  private async executeQuery(sql: string, params: any[], type: 'run' | 'get' | 'all'): Promise<any> {
    if (!this.db) {
      await this.init();
    }

    // Parsing básico de SQL para IndexedDB
    const sqlLower = sql.toLowerCase().trim();

    if (sqlLower.startsWith('select')) {
      return this.handleSelect(sql, params, type);
    } else if (sqlLower.startsWith('insert')) {
      return this.handleInsert(sql, params);
    } else if (sqlLower.startsWith('update')) {
      return this.handleUpdate(sql, params);
    } else if (sqlLower.startsWith('delete')) {
      return this.handleDelete(sql, params);
    }

    return type === 'all' ? [] : null;
  }

  private async handleSelect(sql: string, params: any[], type: 'get' | 'all'): Promise<any> {
    const tableName = this.extractTableName(sql);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([tableName], 'readonly');
      const store = transaction.objectStore(tableName);

      // Verificar se há condições WHERE
      if (sql.includes('WHERE') && params.length > 0) {
        // Para consultas com WHERE, usar índices quando possível
        if (sql.includes('id = ?')) {
          const request = store.get(params[0]);
          request.onsuccess = () => {
            const result = request.result;
            if (type === 'get') {
              resolve(result || null);
            } else {
              resolve(result ? [result] : []);
            }
          };
          request.onerror = () => reject(request.error);
        } else {
          // Para outras consultas, buscar todos e filtrar
          const request = store.getAll();
          request.onsuccess = () => {
            let results = request.result;

            // Filtrar resultados baseado nos parâmetros
            if (sql.includes('cliente_id = ?')) {
              results = results.filter((item: any) => item.cliente_id === params[0]);
            } else if (sql.includes('treino_id = ?')) {
              results = results.filter((item: any) => item.treino_id === params[0]);
            } else if (sql.includes('exercicio_id = ?')) {
              results = results.filter((item: any) => item.exercicio_id === params[0]);
            } else if (sql.includes('avaliacao_id = ?')) {
              results = results.filter((item: any) => item.avaliacao_id === params[0]);
            }

            // Aplicar LIKE para buscas de texto
            if (sql.includes('LIKE')) {
              const searchTerm = params[0]?.replace(/%/g, '');
              if (searchTerm) {
                results = results.filter((item: any) =>
                  Object.values(item).some(value =>
                    String(value).toLowerCase().includes(searchTerm.toLowerCase())
                  )
                );
              }
            }

            // Ordenação básica
            if (sql.includes('ORDER BY nome')) {
              results.sort((a: any, b: any) => a.nome?.localeCompare(b.nome) || 0);
            } else if (sql.includes('ORDER BY data_')) {
              results.sort((a: any, b: any) => {
                const dateA = new Date(a.data_avaliacao || a.data_inicio || a.data_cadastro || 0);
                const dateB = new Date(b.data_avaliacao || b.data_inicio || b.data_cadastro || 0);
                return dateB.getTime() - dateA.getTime();
              });
            }

            if (type === 'get') {
              resolve(results[0] || null);
            } else {
              resolve(results);
            }
          };
          request.onerror = () => reject(request.error);
        }
      } else {
        // Consulta sem WHERE - buscar todos
        const request = store.getAll();
        request.onsuccess = () => {
          let results = request.result;

          // Ordenação
          if (sql.includes('ORDER BY nome')) {
            results.sort((a: any, b: any) => a.nome?.localeCompare(b.nome) || 0);
          } else if (sql.includes('ORDER BY data_')) {
            results.sort((a: any, b: any) => {
              const dateA = new Date(a.data_avaliacao || a.data_inicio || a.data_cadastro || 0);
              const dateB = new Date(b.data_avaliacao || b.data_inicio || b.data_cadastro || 0);
              return dateB.getTime() - dateA.getTime();
            });
          }

          if (type === 'get') {
            resolve(results[0] || null);
          } else {
            resolve(results);
          }
        };
        request.onerror = () => reject(request.error);
      }
    });
  }

  private async handleInsert(sql: string, params: any[]): Promise<any> {
    const tableName = this.extractTableName(sql);
    const data = this.parseInsertData(sql, params);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);

      // Adicionar timestamp se necessário
      if (tableName === 'clientes' && !data.data_cadastro) {
        data.data_cadastro = new Date().toISOString().split('T')[0];
      }
      if (tableName === 'treinos' && !data.data_criacao) {
        data.data_criacao = new Date().toISOString().split('T')[0];
      }

      const request = store.add(data);

      request.onsuccess = () => {
        resolve({ lastInsertRowid: request.result, changes: 1 });
      };

      request.onerror = () => reject(request.error);
    });
  }

  private async handleUpdate(sql: string, params: any[]): Promise<any> {
    const tableName = this.extractTableName(sql);
    const id = params[params.length - 1]; // ID é sempre o último parâmetro

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);

      // Primeiro, buscar o registro existente
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const existingData = getRequest.result;
        if (!existingData) {
          resolve({ changes: 0 });
          return;
        }

        // Atualizar os campos
        const updatedData = { ...existingData };
        const setClause = sql.match(/SET\s+(.+?)\s+WHERE/i)?.[1];
        if (setClause) {
          const assignments = setClause.split(',');
          let paramIndex = 0;

          assignments.forEach(assignment => {
            const [field] = assignment.trim().split('=');
            const fieldName = field.trim();
            updatedData[fieldName] = params[paramIndex];
            paramIndex++;
          });
        }

        // Salvar o registro atualizado
        const putRequest = store.put(updatedData);
        putRequest.onsuccess = () => {
          resolve({ changes: 1 });
        };
        putRequest.onerror = () => reject(putRequest.error);
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  private async handleDelete(sql: string, params: any[]): Promise<any> {
    const tableName = this.extractTableName(sql);
    const id = params[0];

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);

      const request = store.delete(id);

      request.onsuccess = () => {
        resolve({ changes: 1 });
      };

      request.onerror = () => reject(request.error);
    });
  }

  private extractTableName(sql: string): string {
    const match = sql.match(/(?:from|into|update)\s+(\w+)/i);
    return match ? match[1] : 'clientes';
  }

  private parseInsertData(sql: string, params: any[]): any {
    const data: any = {};

    // Extrair colunas da query INSERT
    const columnsMatch = sql.match(/\(([^)]+)\)/);
    if (columnsMatch) {
      const columns = columnsMatch[1].split(',').map(c => c.trim());

      columns.forEach((col, index) => {
        if (params[index] !== undefined && params[index] !== null) {
          data[col] = params[index];
        }
      });
    }

    return data;
  }

  pragma(command: string) {
    // No-op para compatibilidade
  }

  exec(sql: string) {
    // No-op para compatibilidade
  }
}

let db: any;

export const getDatabase = (): any => {
  if (!db) {
    try {
      if (isElectron && Database) {
        console.log('Conectando ao banco de dados SQLite (Electron)...');
        db = new Database(dbPath);
        db.pragma('foreign_keys = ON');
      } else {
        console.log('Conectando ao banco de dados IndexedDB (Navegador)...');
        db = new BrowserDatabase();
        // Inicializar o IndexedDB
        db.init().catch((error: any) => {
          console.error('Erro ao inicializar IndexedDB:', error);
        });
      }
    } catch (error) {
      console.error('Erro ao conectar ao banco de dados:', error);
      throw error;
    }
  }
  return db;
};

export const closeDatabase = () => {
  if (db && typeof db.close === 'function') {
    try {
      db.close();
    } catch (error) {
      console.error('Erro ao fechar o banco de dados:', error);
    }
  }
};

export default getDatabase(); 