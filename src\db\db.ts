import path from 'path';

// Importação do better-sqlite3
let Database: any = null;
try {
  // eslint-disable-next-line
  Database = require('better-sqlite3');
} catch (error) {
  console.error('Erro ao importar better-sqlite3:', error);
  throw new Error('better-sqlite3 é obrigatório para o funcionamento da aplicação');
}

const isDevelopment = process.env.NODE_ENV === 'development';
const dbPath = isDevelopment ? './database.sqlite' : path.join(process.cwd(), 'database.sqlite');

let db: any;

export const getDatabase = (): any => {
  if (!db) {
    try {
      console.log('Conectando ao banco de dados SQLite...');
      db = new Database(dbPath);
      db.pragma('foreign_keys = ON');
    } catch (error) {
      console.error('Erro ao conectar ao banco de dados:', error);
      throw error;
    }
  }
  return db;
};

export const closeDatabase = () => {
  if (db && typeof db.close === 'function') {
    try {
      db.close();
    } catch (error) {
      console.error('Erro ao fechar o banco de dados:', error);
    }
  }
};

export default getDatabase(); 