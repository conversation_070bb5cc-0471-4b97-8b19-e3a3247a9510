import path from 'path';

// Importação condicional do better-sqlite3
let Database: any = null;
if (typeof window !== 'undefined' && 
    window.navigator && 
    /electron/i.test(window.navigator.userAgent)) {
  try {
    // eslint-disable-next-line
    Database = require('better-sqlite3');
  } catch (error) {
    console.error('Erro ao importar better-sqlite3:', error);
  }
}

const isDevelopment = process.env.NODE_ENV === 'development';
const isElectron = typeof window !== 'undefined' && 
                  window.navigator && 
                  /electron/i.test(window.navigator.userAgent);

const dbPath = isDevelopment ? './database.sqlite' : path.join(process.cwd(), 'database.sqlite');

// Mock do banco de dados para desenvolvimento no navegador
const createMockDatabase = () => {
  const mockDb: any = {
    prepare: () => ({
      run: (...args: any[]) => ({ lastInsertRowid: Math.floor(Math.random() * 1000) + 1, changes: 1 }),
      get: () => null,
      all: () => []
    }),
    pragma: () => {},
    exec: () => {},
    close: () => {},
    transaction: (fn: Function) => fn
  };
  return mockDb;
};

let db: any;

export const getDatabase = (): any => {
  if (!db) {
    try {
      // Se estiver em desenvolvimento no navegador, use o mock
      if (isDevelopment && !isElectron) {
        console.log('Usando banco de dados mock para desenvolvimento');
        db = createMockDatabase();
      } else if (Database) {
        // Se estiver no Electron, use o SQLite real
        db = new Database(dbPath);
        db.pragma('foreign_keys = ON');
      } else {
        console.log('SQLite não disponível, usando mock como fallback');
        db = createMockDatabase();
      }
    } catch (error) {
      console.error('Erro ao conectar ao banco de dados:', error);
      console.log('Usando banco de dados mock como fallback');
      db = createMockDatabase();
    }
  }
  return db;
};

export const closeDatabase = () => {
  if (db && typeof db.close === 'function') {
    try {
      db.close();
    } catch (error) {
      console.error('Erro ao fechar o banco de dados:', error);
    }
  }
};

export default getDatabase(); 