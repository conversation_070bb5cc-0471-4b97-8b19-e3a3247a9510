import React, { createContext, useContext, useState, useCallback } from 'react';
import { AvaliacaoFisicaModel, AvaliacaoFisica, DobrasCutaneas, MedidasAntropometricas } from '../models/AvaliacaoFisica';

interface AvaliacaoFisicaContextType {
  avaliacoes: AvaliacaoFisica[];
  avaliacaoSelecionada: AvaliacaoFisica | null;
  carregando: boolean;
  erro: string | null;
  carregarAvaliacoes: () => Promise<void>;
  criarAvaliacao: (avaliacao: AvaliacaoFisica, dobras?: DobrasCutaneas, medidas?: MedidasAntropometricas) => Promise<number>;
  atualizarAvaliacao: (id: number, avaliacao: Partial<AvaliacaoFisica>, dobras?: Partial<DobrasCutaneas>, medidas?: Partial<MedidasAntropometricas>) => Promise<boolean>;
  excluirAvaliacao: (id: number) => Promise<boolean>;
  buscarAvaliacoes: (termo: string) => Promise<void>;
  selecionarAvaliacao: (avaliacao: AvaliacaoFisica | null) => void;
}

const AvaliacaoFisicaContext = createContext<AvaliacaoFisicaContextType | undefined>(undefined);

export const useAvaliacaoFisica = () => {
  const context = useContext(AvaliacaoFisicaContext);
  if (!context) {
    throw new Error('useAvaliacaoFisica deve ser usado dentro de um AvaliacaoFisicaProvider');
  }
  return context;
};

export const AvaliacaoFisicaProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [avaliacoes, setAvaliacoes] = useState<AvaliacaoFisica[]>([]);
  const [avaliacaoSelecionada, setAvaliacaoSelecionada] = useState<AvaliacaoFisica | null>(null);
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const carregarAvaliacoes = useCallback(async () => {
    try {
      setCarregando(true);
      setErro(null);
      const avaliacoesCarregadas = await AvaliacaoFisicaModel.obterTodas();
      setAvaliacoes(avaliacoesCarregadas);
    } catch (error) {
      setErro('Erro ao carregar avaliações físicas');
      console.error('Erro ao carregar avaliações:', error);
    } finally {
      setCarregando(false);
    }
  }, []);

  const criarAvaliacao = useCallback(async (
    avaliacao: AvaliacaoFisica,
    dobras?: DobrasCutaneas,
    medidas?: MedidasAntropometricas
  ): Promise<number> => {
    try {
      setCarregando(true);
      setErro(null);
      const avaliacaoCompleta: AvaliacaoFisica = {
        ...avaliacao,
        dobras_cutaneas: dobras || {
          avaliacao_id: 0,
          peitoral: 0,
          tricipital: 0,
          bicipital: 0,
          axilar_media: 0,
          suprailiaca: 0,
          abdominal: 0,
          coxa: 0,
          panturrilha: 0,
          percentual_gordura: 0
        },
        medidas_antropometricas: medidas || {
          avaliacao_id: 0,
          braco_direito: 0,
          braco_esquerdo: 0,
          antebraco_direito: 0,
          antebraco_esquerdo: 0,
          peitoral: 0,
          cintura: 0,
          abdomen: 0,
          quadril: 0,
          coxa_direita: 0,
          coxa_esquerda: 0,
          panturrilha_direita: 0,
          panturrilha_esquerda: 0
        }
      };
      const novaAvaliacaoId = await AvaliacaoFisicaModel.criar(avaliacaoCompleta);
      await carregarAvaliacoes();
      return novaAvaliacaoId;
    } catch (error) {
      setErro('Erro ao criar avaliação física');
      console.error('Erro ao criar avaliação:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [carregarAvaliacoes]);

  const atualizarAvaliacao = useCallback(async (
    id: number,
    avaliacao: Partial<AvaliacaoFisica>,
    dobras?: Partial<DobrasCutaneas>,
    medidas?: Partial<MedidasAntropometricas>
  ): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      
      const avaliacaoCompleta: Partial<AvaliacaoFisica> = {
        ...avaliacao
      };
      
      if (dobras) {
        avaliacaoCompleta.dobras_cutaneas = dobras as DobrasCutaneas;
      }
      
      if (medidas) {
        avaliacaoCompleta.medidas_antropometricas = medidas as MedidasAntropometricas;
      }
      
      await AvaliacaoFisicaModel.atualizar(id, avaliacaoCompleta);
      await carregarAvaliacoes();
      return true;
    } catch (error) {
      setErro('Erro ao atualizar avaliação física');
      console.error('Erro ao atualizar avaliação:', error);
      return false;
    } finally {
      setCarregando(false);
    }
  }, [carregarAvaliacoes]);

  const excluirAvaliacao = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      await AvaliacaoFisicaModel.excluir(id);
      await carregarAvaliacoes();
      if (avaliacaoSelecionada?.id === id) {
        setAvaliacaoSelecionada(null);
      }
      return true;
    } catch (error) {
      setErro('Erro ao excluir avaliação física');
      console.error('Erro ao excluir avaliação:', error);
      return false;
    } finally {
      setCarregando(false);
    }
  }, [carregarAvaliacoes, avaliacaoSelecionada]);

  const buscarAvaliacoes = useCallback(async (termo: string) => {
    try {
      setCarregando(true);
      setErro(null);
      const resultados = await AvaliacaoFisicaModel.buscar(termo);
      setAvaliacoes(resultados);
    } catch (error) {
      setErro('Erro ao buscar avaliações físicas');
      console.error('Erro ao buscar avaliações:', error);
    } finally {
      setCarregando(false);
    }
  }, []);

  const selecionarAvaliacao = useCallback((avaliacao: AvaliacaoFisica | null) => {
    setAvaliacaoSelecionada(avaliacao);
  }, []);

  const value = {
    avaliacoes,
    avaliacaoSelecionada,
    carregando,
    erro,
    carregarAvaliacoes,
    criarAvaliacao,
    atualizarAvaliacao,
    excluirAvaliacao,
    buscarAvaliacoes,
    selecionarAvaliacao,
  };

  return (
    <AvaliacaoFisicaContext.Provider value={value}>
      {children}
    </AvaliacaoFisicaContext.Provider>
  );
};

export default AvaliacaoFisicaContext; 