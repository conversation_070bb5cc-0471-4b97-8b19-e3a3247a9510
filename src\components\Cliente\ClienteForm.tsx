import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  TextField, 
  Grid, 
  Typography, 
  FormControl, 
  FormLabel, 
  RadioGroup, 
  FormControlLabel, 
  Radio,
  Paper,
  Snackbar,
  Alert
} from '@mui/material';
import { Cliente } from '../../models/Cliente';
import { useAppContext } from '../../contexts/AppContext';
import { Container } from '../common/Container';

interface ClienteFormProps {
  clienteParaEditar?: Cliente;
  onSalvar?: () => void;
  onCancelar?: () => void;
}

const ClienteForm: React.FC<ClienteFormProps> = ({ 
  clienteParaEditar, 
  onSalvar, 
  onCancelar 
}) => {
  const { adicionarCliente, atualizarCliente } = useAppContext();
  const [nome, setNome] = useState('');
  const [dataNascimento, setDataNascimento] = useState('');
  const [sexo, setSexo] = useState<'M' | 'F'>('M');
  const [email, setEmail] = useState('');
  const [telefone, setTelefone] = useState('');
  const [observacoes, setObservacoes] = useState('');
  const [erros, setErros] = useState<Record<string, string>>({});
  const [snackbar, setSnackbar] = useState({ aberto: false, mensagem: '', tipo: 'success' as 'success' | 'error' });

  // Preencher o formulário se estiver editando um cliente
  useEffect(() => {
    if (clienteParaEditar) {
      setNome(clienteParaEditar.nome);
      setDataNascimento(clienteParaEditar.data_nascimento);
      setSexo(clienteParaEditar.sexo);
      setEmail(clienteParaEditar.email || '');
      setTelefone(clienteParaEditar.telefone || '');
      setObservacoes(clienteParaEditar.observacoes || '');
    }
  }, [clienteParaEditar]);

  const validarFormulario = (): boolean => {
    const novosErros: Record<string, string> = {};

    if (!nome.trim()) {
      novosErros.nome = 'Nome é obrigatório';
    }

    if (!dataNascimento) {
      novosErros.dataNascimento = 'Data de nascimento é obrigatória';
    } else {
      // Validar formato da data
      const dataRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dataRegex.test(dataNascimento)) {
        novosErros.dataNascimento = 'Formato de data inválido (AAAA-MM-DD)';
      }
    }

    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      novosErros.email = 'Email inválido';
    }

    setErros(novosErros);
    return Object.keys(novosErros).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validarFormulario()) {
      return;
    }

    try {
      const cliente: Cliente = {
        nome,
        data_nascimento: dataNascimento,
        sexo,
        email: email || undefined,
        telefone: telefone || undefined,
        data_cadastro: clienteParaEditar?.data_cadastro || new Date().toISOString().split('T')[0],
        observacoes: observacoes || undefined
      };

      if (clienteParaEditar?.id) {
        // Atualizar cliente existente
        await atualizarCliente(clienteParaEditar.id, cliente);
        setSnackbar({ 
          aberto: true, 
          mensagem: 'Cliente atualizado com sucesso!', 
          tipo: 'success' 
        });
      } else {
        // Adicionar novo cliente
        await adicionarCliente(cliente);
        setSnackbar({ 
          aberto: true, 
          mensagem: 'Cliente adicionado com sucesso!', 
          tipo: 'success' 
        });
        // Limpar formulário após adicionar
        if (!clienteParaEditar) {
          setNome('');
          setDataNascimento('');
          setSexo('M');
          setEmail('');
          setTelefone('');
          setObservacoes('');
        }
      }

      if (onSalvar) {
        onSalvar();
      }
    } catch (error) {
      console.error('Erro ao salvar cliente:', error);
      setSnackbar({ 
        aberto: true, 
        mensagem: 'Erro ao salvar cliente', 
        tipo: 'error' 
      });
    }
  };

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, aberto: false });
  };

  return (
    <Container
      title={clienteParaEditar ? "Editar Cliente" : "Novo Cliente"}
      subtitle="Preencha os dados do cliente"
      useGradient
    >
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="Nome"
              value={nome}
              onChange={(e) => setNome(e.target.value)}
              error={!!erros.nome}
              helperText={erros.nome}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="Data de Nascimento"
              type="date"
              value={dataNascimento}
              onChange={(e) => setDataNascimento(e.target.value)}
              error={!!erros.dataNascimento}
              helperText={erros.dataNascimento || 'AAAA-MM-DD'}
              margin="normal"
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl component="fieldset" margin="normal">
              <FormLabel component="legend">Sexo</FormLabel>
              <RadioGroup
                row
                value={sexo}
                onChange={(e) => setSexo(e.target.value as 'M' | 'F')}
              >
                <FormControlLabel value="M" control={<Radio />} label="Masculino" />
                <FormControlLabel value="F" control={<Radio />} label="Feminino" />
              </RadioGroup>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={!!erros.email}
              helperText={erros.email}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Telefone"
              value={telefone}
              onChange={(e) => setTelefone(e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Observações"
              multiline
              rows={4}
              value={observacoes}
              onChange={(e) => setObservacoes(e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            {onCancelar && (
              <Button 
                onClick={onCancelar} 
                sx={{ mr: 1 }}
              >
                Cancelar
              </Button>
            )}
            <Button 
              type="submit" 
              variant="contained" 
              color="primary"
            >
              {clienteParaEditar ? 'Atualizar' : 'Salvar'}
            </Button>
          </Grid>
        </Grid>
      </form>
      <Snackbar 
        open={snackbar.aberto} 
        autoHideDuration={6000} 
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbar.tipo} 
          sx={{ width: '100%' }}
        >
          {snackbar.mensagem}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ClienteForm; 