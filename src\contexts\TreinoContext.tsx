import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { TreinoModel, Treino, Exercicio, Serie } from '../models/Treino';

interface TreinoContextType {
  treinos: Treino[];
  treinoSelecionado: Treino | null;
  exercicios: Exercicio[];
  carregando: boolean;
  erro: string | null;
  carregarTreinosPorCliente: (clienteId: number) => Promise<void>;
  carregarExerciciosPorTreino: (treinoId: number, tipoTreino: string) => Promise<void>;
  criarTreino: (treino: Treino) => Promise<number>;
  atualizarTreino: (id: number, treino: Partial<Treino>) => Promise<boolean>;
  excluirTreino: (id: number) => Promise<boolean>;
  selecionarTreino: (treino: Treino | null) => void;
  criarExercicio: (exercicio: Exercicio) => Promise<number>;
  atualizarExercicio: (id: number, exercicio: Partial<Exercicio>) => Promise<boolean>;
  excluirExercicio: (id: number) => Promise<boolean>;
  criarSerie: (serie: Serie) => Promise<number>;
  atualizarSerie: (id: number, serie: Partial<Serie>) => Promise<boolean>;
  excluirSerie: (id: number) => Promise<boolean>;
  obterSeriesPorExercicio: (exercicioId: number, semana?: number) => Promise<Serie[]>;
}

const TreinoContext = createContext<TreinoContextType | undefined>(undefined);

export const useTreino = () => {
  const context = useContext(TreinoContext);
  if (!context) {
    throw new Error('useTreino deve ser usado dentro de um TreinoProvider');
  }
  return context;
};

export const TreinoProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [treinos, setTreinos] = useState<Treino[]>([]);
  const [treinoSelecionado, setTreinoSelecionado] = useState<Treino | null>(null);
  const [exercicios, setExercicios] = useState<Exercicio[]>([]);
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const carregarTreinosPorCliente = useCallback(async (clienteId: number): Promise<void> => {
    try {
      setCarregando(true);
      setErro(null);
      const treinosCarregados = TreinoModel.obterTreinosPorCliente(clienteId);
      setTreinos(treinosCarregados);
    } catch (error) {
      setErro('Erro ao carregar treinos');
      console.error('Erro ao carregar treinos:', error);
    } finally {
      setCarregando(false);
    }
  }, []);

  const carregarExerciciosPorTreino = useCallback(async (treinoId: number, tipoTreino: string): Promise<void> => {
    try {
      setCarregando(true);
      setErro(null);
      const todosExercicios = TreinoModel.obterExerciciosPorTreino(treinoId);
      const exerciciosFiltrados = tipoTreino 
        ? todosExercicios.filter(e => e.tipo_treino === tipoTreino)
        : todosExercicios;
      setExercicios(exerciciosFiltrados);
    } catch (error) {
      setErro('Erro ao carregar exercícios');
      console.error('Erro ao carregar exercícios:', error);
    } finally {
      setCarregando(false);
    }
  }, []);

  const criarTreino = useCallback(async (treino: Treino): Promise<number> => {
    try {
      setCarregando(true);
      setErro(null);
      const id = TreinoModel.criarTreino(treino);
      
      if (treinos.length > 0) {
        setTreinos(prevTreinos => [...prevTreinos, { ...treino, id }]);
      }
      
      return id;
    } catch (error) {
      setErro('Erro ao criar treino');
      console.error('Erro ao criar treino:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [treinos]);

  const atualizarTreino = useCallback(async (id: number, treino: Partial<Treino>): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      const sucesso = TreinoModel.atualizarTreino(id, treino);
      
      if (sucesso) {
        setTreinos(prevTreinos => prevTreinos.map(t => t.id === id ? { ...t, ...treino } : t));
        
        if (treinoSelecionado?.id === id) {
          setTreinoSelecionado(prevTreino => prevTreino ? { ...prevTreino, ...treino } : null);
        }
      }
      
      return sucesso;
    } catch (error) {
      setErro('Erro ao atualizar treino');
      console.error('Erro ao atualizar treino:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [treinoSelecionado]);

  const excluirTreino = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      const sucesso = TreinoModel.excluirTreino(id);
      
      if (sucesso) {
        setTreinos(prevTreinos => prevTreinos.filter(t => t.id !== id));
        
        if (treinoSelecionado?.id === id) {
          setTreinoSelecionado(null);
        }
      }
      
      return sucesso;
    } catch (error) {
      setErro('Erro ao excluir treino');
      console.error('Erro ao excluir treino:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [treinoSelecionado]);

  const selecionarTreino = useCallback((treino: Treino | null): void => {
    setTreinoSelecionado(treino);
  }, []);

  const criarExercicio = useCallback(async (exercicio: Exercicio): Promise<number> => {
    try {
      setCarregando(true);
      setErro(null);
      const id = TreinoModel.criarExercicio(exercicio);
      
      if (exercicios.length > 0 && treinoSelecionado?.id === exercicio.treino_id) {
        setExercicios(prevExercicios => [...prevExercicios, { ...exercicio, id }]);
      }
      
      return id;
    } catch (error) {
      setErro('Erro ao criar exercício');
      console.error('Erro ao criar exercício:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [exercicios, treinoSelecionado]);

  const atualizarExercicio = useCallback(async (id: number, exercicio: Partial<Exercicio>): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      const sucesso = TreinoModel.atualizarExercicio(id, exercicio);
      
      if (sucesso) {
        setExercicios(prevExercicios => prevExercicios.map(e => e.id === id ? { ...e, ...exercicio } : e));
      }
      
      return sucesso;
    } catch (error) {
      setErro('Erro ao atualizar exercício');
      console.error('Erro ao atualizar exercício:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, []);

  const excluirExercicio = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      const sucesso = TreinoModel.excluirExercicio(id);
      
      if (sucesso) {
        setExercicios(prevExercicios => prevExercicios.filter(e => e.id !== id));
      }
      
      return sucesso;
    } catch (error) {
      setErro('Erro ao excluir exercício');
      console.error('Erro ao excluir exercício:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, []);

  const criarSerie = useCallback(async (serie: Serie): Promise<number> => {
    try {
      setCarregando(true);
      setErro(null);
      const id = TreinoModel.criarSerie(serie);
      return id;
    } catch (error) {
      setErro('Erro ao criar série');
      console.error('Erro ao criar série:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, []);

  const atualizarSerie = useCallback(async (id: number, serie: Partial<Serie>): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      const sucesso = TreinoModel.atualizarSerie(id, serie);
      return sucesso;
    } catch (error) {
      setErro('Erro ao atualizar série');
      console.error('Erro ao atualizar série:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, []);

  const excluirSerie = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);
      const sucesso = TreinoModel.excluirSerie(id);
      return sucesso;
    } catch (error) {
      setErro('Erro ao excluir série');
      console.error('Erro ao excluir série:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, []);

  const obterSeriesPorExercicio = useCallback(async (exercicioId: number, semana?: number): Promise<Serie[]> => {
    try {
      setCarregando(true);
      setErro(null);
      const todasSeries = TreinoModel.obterSeriesPorExercicio(exercicioId);
      const seriesFiltradas = semana ? todasSeries.filter(s => s.semana === semana) : todasSeries;
      return seriesFiltradas;
    } catch (error) {
      setErro('Erro ao obter séries');
      console.error('Erro ao obter séries:', error);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, []);

  return (
    <TreinoContext.Provider
      value={{
        treinos,
        treinoSelecionado,
        exercicios,
        carregando,
        erro,
        carregarTreinosPorCliente,
        carregarExerciciosPorTreino,
        criarTreino,
        atualizarTreino,
        excluirTreino,
        selecionarTreino,
        criarExercicio,
        atualizarExercicio,
        excluirExercicio,
        criarSerie,
        atualizarSerie,
        excluirSerie,
        obterSeriesPorExercicio
      }}
    >
      {children}
    </TreinoContext.Provider>
  );
};

export default TreinoProvider; 