import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Grid,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Paper
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { Treino } from '../../models/Treino';
import { Cliente } from '../../models/Cliente';
import { Container } from '../common/Container';

interface TreinoFormProps {
  treino?: Treino;
  cliente?: Cliente;
  onSave: (treino: Treino) => void;
  onCancel: () => void;
}

export const TreinoForm = ({
  treino,
  cliente,
  onSave,
  onCancel
}: TreinoFormProps) => {
  const [formData, setFormData] = useState<Partial<Treino>>({
    nome: '',
    data_inicio: new Date().toISOString().split('T')[0],
    data_fim: '',
    observacoes: ''
  });
  
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    if (treino) {
      setFormData({
        ...treino,
        data_inicio: treino.data_inicio ? treino.data_inicio.split('T')[0] : new Date().toISOString().split('T')[0],
        data_fim: treino.data_fim ? treino.data_fim.split('T')[0] : ''
      });
    } else {
      setFormData({
        nome: '',
        data_inicio: new Date().toISOString().split('T')[0],
        data_fim: '',
        observacoes: ''
      });
    }
  }, [treino]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Limpar erro quando o campo for preenchido
    if (errors[name] && value.trim() !== '') {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.nome || formData.nome.trim() === '') {
      newErrors.nome = 'O nome do treino é obrigatório';
    }
    
    if (!formData.data_inicio || formData.data_inicio.trim() === '') {
      newErrors.data_inicio = 'A data de início é obrigatória';
    }
    
    if (formData.data_fim && formData.data_inicio && new Date(formData.data_fim) < new Date(formData.data_inicio)) {
      newErrors.data_fim = 'A data de término deve ser posterior à data de início';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    const treinoData: Treino = {
      ...formData as Treino,
      cliente_id: cliente?.id || treino?.cliente_id || 0
    };
    
    onSave(treinoData);
  };

  return (
    <Container
      title={treino ? "Editar Treino" : "Novo Treino"}
      subtitle="Defina os exercícios e séries do treino"
      useGradient
    >
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Nome do Treino"
              name="nome"
              value={formData.nome || ''}
              onChange={handleChange}
              error={!!errors.nome}
              helperText={errors.nome}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Data de Início"
              name="data_inicio"
              type="date"
              value={formData.data_inicio || ''}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
              error={!!errors.data_inicio}
              helperText={errors.data_inicio}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Data de Término"
              name="data_fim"
              type="date"
              value={formData.data_fim || ''}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
              error={!!errors.data_fim}
              helperText={errors.data_fim}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Observações"
              name="observacoes"
              value={formData.observacoes || ''}
              onChange={handleChange}
              multiline
              rows={4}
            />
          </Grid>
          
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={onCancel}
              startIcon={<CancelIcon />}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
            >
              {treino ? 'Atualizar' : 'Salvar'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};

export default TreinoForm; 