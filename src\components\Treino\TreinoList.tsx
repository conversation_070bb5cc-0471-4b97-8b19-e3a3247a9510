import React, { useState, useEffect } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Paper,
  Divider,
  TextField,
  InputAdornment,
  Tooltip,
  CircularProgress
} from '@mui/material';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SearchIcon from '@mui/icons-material/Search';
import { Treino } from '../../models/Treino';
import { useTreino } from '../../contexts/TreinoContext';

interface TreinoListProps {
  clienteId?: number;
  onSelectTreino: (treino: Treino) => void;
  onEditTreino?: (treino: Treino) => void;
  onDeleteTreino?: (id: number) => void;
}

const TreinoList: React.FC<TreinoListProps> = ({
  clienteId,
  onSelectTreino,
  onEditTreino,
  onDeleteTreino
}) => {
  const { treinos, carregando, carregarTreinosPorCliente } = useTreino();
  const [filteredTreinos, setFilteredTreinos] = useState<Treino[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (clienteId) {
      carregarTreinosPorCliente(clienteId);
    }
  }, [clienteId, carregarTreinosPorCliente]);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredTreinos(treinos);
    } else {
      const filtered = treinos.filter(treino =>
        treino.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (treino.observacoes && treino.observacoes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredTreinos(filtered);
    }
  }, [treinos, searchTerm]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <Paper elevation={2} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Treinos
        </Typography>
        <TextField
          fullWidth
          placeholder="Buscar treinos..."
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />
      </Box>
      
      <Divider />
      
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>
        {carregando ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : filteredTreinos.length === 0 ? (
          <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
            {treinos.length === 0 
              ? 'Nenhum treino encontrado para este cliente.' 
              : 'Nenhum treino corresponde à sua busca.'}
          </Typography>
        ) : (
          <List>
            {filteredTreinos.map((treino) => (
              <ListItem
                key={treino.id}
                button
                onClick={() => onSelectTreino(treino)}
                sx={{ 
                  borderRadius: 1,
                  mb: 1,
                  '&:hover': { bgcolor: 'action.hover' }
                }}
              >
                <ListItemIcon>
                  <FitnessCenterIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary={treino.nome}
                  secondary={
                    <>
                      <Typography component="span" variant="body2" color="text.primary">
                        Início: {formatDate(treino.data_inicio)}
                      </Typography>
                      {treino.data_fim && (
                        <>
                          {' • '}
                          <Typography component="span" variant="body2" color="text.primary">
                            Fim: {formatDate(treino.data_fim)}
                          </Typography>
                        </>
                      )}
                    </>
                  }
                />
                <ListItemSecondaryAction>
                  {onEditTreino && (
                    <Tooltip title="Editar">
                      <IconButton 
                        edge="end" 
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditTreino(treino);
                        }}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                  {onDeleteTreino && (
                    <Tooltip title="Excluir">
                      <IconButton 
                        edge="end" 
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteTreino(treino.id!);
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Paper>
  );
};

export default TreinoList; 