import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Cliente, ClienteModel } from '../models/Cliente';
import { initDatabase } from '../db/init';

interface AppContextType {
  clientes: Cliente[];
  clienteSelecionado: Cliente | null;
  carregando: boolean;
  erro: string | null;
  buscarClientes: () => Promise<void>;
  selecionarCliente: (cliente: Cliente | null) => void;
  adicionarCliente: (cliente: Cliente) => Promise<number>;
  atualizarCliente: (id: number, cliente: Partial<Cliente>) => Promise<boolean>;
  excluirCliente: (id: number) => Promise<boolean>;
  buscarClientesPorTermo: (termo: string) => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext deve ser usado dentro de um AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [clientes, setClientes] = useState<Cliente[]>([]);
  const [clienteSelecionado, setClienteSelecionado] = useState<Cliente | null>(null);
  const [carregando, setCarregando] = useState<boolean>(true);
  const [erro, setErro] = useState<string | null>(null);
  const [dbInicializado, setDbInicializado] = useState<boolean>(false);

  // Inicializar o banco de dados
  useEffect(() => {
    const inicializarBancoDados = async () => {
      try {
        await initDatabase();
        setDbInicializado(true);
      } catch (error) {
        console.error('Erro ao inicializar o banco de dados:', error);
        setErro('Erro ao inicializar o banco de dados');
      }
    };

    inicializarBancoDados();
  }, []);

  // Carregar clientes quando o banco de dados for inicializado
  useEffect(() => {
    if (dbInicializado) {
      buscarClientes();
    }
  }, [dbInicializado]);

  const buscarClientes = async () => {
    setCarregando(true);
    try {
      const clientesData = ClienteModel.obterTodos();
      setClientes(clientesData);
      setErro(null);
    } catch (error) {
      console.error('Erro ao buscar clientes:', error);
      setErro('Erro ao buscar clientes');
    } finally {
      setCarregando(false);
    }
  };

  const selecionarCliente = (cliente: Cliente | null) => {
    setClienteSelecionado(cliente);
  };

  const adicionarCliente = async (cliente: Cliente): Promise<number> => {
    try {
      const id = ClienteModel.criar(cliente);
      await buscarClientes();
      return id;
    } catch (error) {
      console.error('Erro ao adicionar cliente:', error);
      setErro('Erro ao adicionar cliente');
      throw error;
    }
  };

  const atualizarCliente = async (id: number, cliente: Partial<Cliente>): Promise<boolean> => {
    try {
      const sucesso = ClienteModel.atualizar(id, cliente);
      if (sucesso) {
        await buscarClientes();
        
        // Atualizar o cliente selecionado se for o mesmo que está sendo editado
        if (clienteSelecionado && clienteSelecionado.id === id) {
          const clienteAtualizado = ClienteModel.obterPorId(id);
          if (clienteAtualizado) {
            setClienteSelecionado(clienteAtualizado);
          }
        }
      }
      return sucesso;
    } catch (error) {
      console.error('Erro ao atualizar cliente:', error);
      setErro('Erro ao atualizar cliente');
      throw error;
    }
  };

  const excluirCliente = async (id: number): Promise<boolean> => {
    try {
      const sucesso = ClienteModel.excluir(id);
      if (sucesso) {
        await buscarClientes();
        
        // Limpar o cliente selecionado se for o mesmo que está sendo excluído
        if (clienteSelecionado && clienteSelecionado.id === id) {
          setClienteSelecionado(null);
        }
      }
      return sucesso;
    } catch (error) {
      console.error('Erro ao excluir cliente:', error);
      setErro('Erro ao excluir cliente');
      throw error;
    }
  };

  const buscarClientesPorTermo = async (termo: string): Promise<void> => {
    setCarregando(true);
    try {
      if (termo.trim() === '') {
        await buscarClientes();
      } else {
        const clientesEncontrados = ClienteModel.buscar(termo);
        setClientes(clientesEncontrados);
      }
      setErro(null);
    } catch (error) {
      console.error('Erro ao buscar clientes por termo:', error);
      setErro('Erro ao buscar clientes');
    } finally {
      setCarregando(false);
    }
  };

  const value: AppContextType = {
    clientes,
    clienteSelecionado,
    carregando,
    erro,
    buscarClientes,
    selecionarCliente,
    adicionarCliente,
    atualizarCliente,
    excluirCliente,
    buscarClientesPorTermo
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export default AppContext; 