import { getDatabase } from '../db/db';

export interface DobrasCutaneasBase {
  peitoral: number;
  tricipital: number;
  bicipital: number;
  axilar_media: number;
  suprailiaca: number;
  abdominal: number;
  coxa: number;
  panturrilha: number;
  percentual_gordura: number;
}

export interface DobrasCutaneas extends DobrasCutaneasBase {
  id?: number;
  avaliacao_id: number;
}

export interface MedidasAntropometricasBase {
  braco_direito: number;
  braco_esquerdo: number;
  antebraco_direito: number;
  antebraco_esquerdo: number;
  peitoral: number;
  cintura: number;
  abdomen: number;
  quadril: number;
  coxa_direita: number;
  coxa_esquerda: number;
  panturrilha_direita: number;
  panturrilha_esquerda: number;
}

export interface MedidasAntropometricas extends MedidasAntropometricasBase {
  id?: number;
  avaliacao_id: number;
}

export interface AvaliacaoFisica {
  id?: number;
  cliente_id: number;
  data_avaliacao: string;
  peso: number;
  altura: number;
  idade: number;
  dobras_cutaneas: DobrasCutaneas;
  medidas_antropometricas: MedidasAntropometricas;
  nivelAtividade: 'Sedentário' | 'Levemente Ativo' | 'Moderadamente Ativo' | 'Muito Ativo' | 'Extremamente Ativo';
  intensidadeExercicios: 'Leve' | 'Moderada' | 'Alta' | 'Muito Alta';
  frequenciaSemanal: number;
  tipoTrabalho: 'Sedentário' | 'Leve' | 'Moderado' | 'Intenso';
  objetivo: 'manutenção' | 'perda' | 'ganho';
  percentualGordura: number;
  data?: string;
  circunferencia_abdominal?: number;
  massa_muscular?: number;
}

export class AvaliacaoFisicaModel {
  static async obterPorId(id: number): Promise<AvaliacaoFisica | null> {
    const db = getDatabase();
    try {
      const avaliacao = db.prepare(`
        SELECT * FROM avaliacoes_fisicas WHERE id = ?
      `).get(id);

      if (!avaliacao) return null;

      const dobras = db.prepare(`
        SELECT * FROM dobras_cutaneas WHERE avaliacao_id = ?
      `).get(id);

      const medidas = db.prepare(`
        SELECT * FROM medidas_antropometricas WHERE avaliacao_id = ?
      `).get(id);

      return this.mapearAvaliacao(avaliacao, dobras, medidas);
    } catch (error) {
      console.error('Erro ao obter avaliação física:', error);
      throw error;
    }
  }

  static async obterTodas(): Promise<AvaliacaoFisica[]> {
    try {
      const db = getDatabase();
      const avaliacoes = db.prepare(`
        SELECT * FROM avaliacoes_fisicas ORDER BY data_avaliacao DESC
      `).all();

      return Promise.all(avaliacoes.map(async (avaliacao: any) => {
        const dobras = db.prepare(`
          SELECT * FROM dobras_cutaneas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        const medidas = db.prepare(`
          SELECT * FROM medidas_antropometricas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        return this.mapearAvaliacao(avaliacao, dobras, medidas);
      }));
    } catch (error) {
      console.error('Erro ao obter avaliações físicas:', error);
      return Promise.resolve([]);
    }
  }

  static async criar(avaliacao: AvaliacaoFisica): Promise<number> {
    const db = getDatabase();
    try {
      db.prepare('BEGIN').run();

      const { lastInsertRowid: avaliacaoId } = db.prepare(`
        INSERT INTO avaliacoes_fisicas (cliente_id, data_avaliacao, peso, altura, idade)
        VALUES (?, ?, ?, ?, ?)
      `).run(
        avaliacao.cliente_id,
        avaliacao.data_avaliacao,
        avaliacao.peso,
        avaliacao.altura,
        avaliacao.idade
      );

      if (avaliacao.dobras_cutaneas) {
        db.prepare(`
          INSERT INTO dobras_cutaneas (
            avaliacao_id, peitoral, tricipital, bicipital, axilar_media,
            suprailiaca, abdominal, coxa, panturrilha, percentual_gordura
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          avaliacaoId,
          avaliacao.dobras_cutaneas.peitoral,
          avaliacao.dobras_cutaneas.tricipital,
          avaliacao.dobras_cutaneas.bicipital,
          avaliacao.dobras_cutaneas.axilar_media,
          avaliacao.dobras_cutaneas.suprailiaca,
          avaliacao.dobras_cutaneas.abdominal,
          avaliacao.dobras_cutaneas.coxa,
          avaliacao.dobras_cutaneas.panturrilha,
          avaliacao.dobras_cutaneas.percentual_gordura
        );
      }

      if (avaliacao.medidas_antropometricas) {
        db.prepare(`
          INSERT INTO medidas_antropometricas (
            avaliacao_id, braco_direito, braco_esquerdo, antebraco_direito,
            antebraco_esquerdo, peitoral, cintura, abdomen, quadril,
            coxa_direita, coxa_esquerda, panturrilha_direita, panturrilha_esquerda
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          avaliacaoId,
          avaliacao.medidas_antropometricas.braco_direito,
          avaliacao.medidas_antropometricas.braco_esquerdo,
          avaliacao.medidas_antropometricas.antebraco_direito,
          avaliacao.medidas_antropometricas.antebraco_esquerdo,
          avaliacao.medidas_antropometricas.peitoral,
          avaliacao.medidas_antropometricas.cintura,
          avaliacao.medidas_antropometricas.abdomen,
          avaliacao.medidas_antropometricas.quadril,
          avaliacao.medidas_antropometricas.coxa_direita,
          avaliacao.medidas_antropometricas.coxa_esquerda,
          avaliacao.medidas_antropometricas.panturrilha_direita,
          avaliacao.medidas_antropometricas.panturrilha_esquerda
        );
      }

      db.prepare('COMMIT').run();
      return Number(avaliacaoId);
    } catch (error) {
      db.prepare('ROLLBACK').run();
      console.error('Erro ao criar avaliação física:', error);
      throw error;
    }
  }

  static async atualizar(id: number, avaliacao: Partial<AvaliacaoFisica>): Promise<boolean> {
    try {
      const db = getDatabase();
      const campos = Object.keys(avaliacao).filter(campo => 
        campo !== 'id' && 
        campo !== 'dobras_cutaneas' && 
        campo !== 'medidas_antropometricas'
      );
      
      if (campos.length > 0) {
        const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
        const valores = campos.map(campo => (avaliacao as any)[campo]);
        
        const stmt = db.prepare(`UPDATE avaliacoes_fisicas SET ${setClauses} WHERE id = ?`);
        stmt.run([...valores, id]);
      }
      
      // Atualizar dobras cutâneas
      if (avaliacao.dobras_cutaneas) {
        const dobrasCutaneas = avaliacao.dobras_cutaneas;
        const dobraExistente = db.prepare('SELECT id FROM dobras_cutaneas WHERE avaliacao_id = ?').get(id);
        
        if (dobraExistente) {
          const camposDobras = Object.keys(dobrasCutaneas).filter(campo => campo !== 'id' && campo !== 'avaliacao_id');
          if (camposDobras.length > 0) {
            const setClausesDobras = camposDobras.map(campo => `${campo} = ?`).join(', ');
            const valoresDobras = camposDobras.map(campo => (dobrasCutaneas as any)[campo]);
            
            const stmtDobras = db.prepare(`UPDATE dobras_cutaneas SET ${setClausesDobras} WHERE avaliacao_id = ?`);
            stmtDobras.run([...valoresDobras, id]);
          }
        } else {
          const stmtDobras = db.prepare(`
            INSERT INTO dobras_cutaneas (
              avaliacao_id, peitoral, tricipital, bicipital, axilar_media, 
              suprailiaca, abdominal, coxa, panturrilha, percentual_gordura
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);
          
          stmtDobras.run(
            id,
            dobrasCutaneas.peitoral,
            dobrasCutaneas.tricipital,
            dobrasCutaneas.bicipital,
            dobrasCutaneas.axilar_media,
            dobrasCutaneas.suprailiaca,
            dobrasCutaneas.abdominal,
            dobrasCutaneas.coxa,
            dobrasCutaneas.panturrilha,
            dobrasCutaneas.percentual_gordura
          );
        }
      }
      
      // Atualizar medidas antropométricas
      if (avaliacao.medidas_antropometricas) {
        const medidasAntropometricas = avaliacao.medidas_antropometricas;
        const medidaExistente = db.prepare('SELECT id FROM medidas_antropometricas WHERE avaliacao_id = ?').get(id);
        
        if (medidaExistente) {
          const camposMedidas = Object.keys(medidasAntropometricas).filter(campo => campo !== 'id' && campo !== 'avaliacao_id');
          if (camposMedidas.length > 0) {
            const setClausesMedidas = camposMedidas.map(campo => `${campo} = ?`).join(', ');
            const valoresMedidas = camposMedidas.map(campo => (medidasAntropometricas as any)[campo]);
            
            const stmtMedidas = db.prepare(`UPDATE medidas_antropometricas SET ${setClausesMedidas} WHERE avaliacao_id = ?`);
            stmtMedidas.run([...valoresMedidas, id]);
          }
        } else {
          const stmtMedidas = db.prepare(`
            INSERT INTO medidas_antropometricas (
              avaliacao_id, braco_direito, braco_esquerdo, antebraco_direito, antebraco_esquerdo,
              peitoral, cintura, abdomen, quadril, coxa_direita, coxa_esquerda,
              panturrilha_direita, panturrilha_esquerda
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);
          
          stmtMedidas.run(
            id,
            medidasAntropometricas.braco_direito,
            medidasAntropometricas.braco_esquerdo,
            medidasAntropometricas.antebraco_direito,
            medidasAntropometricas.antebraco_esquerdo,
            medidasAntropometricas.peitoral,
            medidasAntropometricas.cintura,
            medidasAntropometricas.abdomen,
            medidasAntropometricas.quadril,
            medidasAntropometricas.coxa_direita,
            medidasAntropometricas.coxa_esquerda,
            medidasAntropometricas.panturrilha_direita,
            medidasAntropometricas.panturrilha_esquerda
          );
        }
      }
      
      return Promise.resolve(true);
    } catch (error) {
      console.error('Erro ao atualizar avaliação física:', error);
      return Promise.resolve(false);
    }
  }

  static async excluir(id: number): Promise<void> {
    const db = getDatabase();
    try {
      db.prepare('BEGIN').run();

      // Excluir registros relacionados
      db.prepare('DELETE FROM dobras_cutaneas WHERE avaliacao_id = ?').run(id);
      db.prepare('DELETE FROM medidas_antropometricas WHERE avaliacao_id = ?').run(id);
      db.prepare('DELETE FROM avaliacoes_fisicas WHERE id = ?').run(id);

      db.prepare('COMMIT').run();
    } catch (error) {
      db.prepare('ROLLBACK').run();
      console.error('Erro ao excluir avaliação física:', error);
      throw error;
    }
  }

  static async buscar(termo: string): Promise<AvaliacaoFisica[]> {
    try {
      const db = getDatabase();
      const avaliacoes = db.prepare(`
        SELECT af.* FROM avaliacoes_fisicas af
        JOIN clientes c ON af.cliente_id = c.id
        WHERE
          c.nome LIKE ? OR
          af.data_avaliacao LIKE ? OR
          af.id LIKE ?
        ORDER BY af.data_avaliacao DESC
      `).all(
        `%${termo}%`,
        `%${termo}%`,
        `%${termo}%`
      );

      return Promise.all(avaliacoes.map(async (avaliacao: any) => {
        const dobras = db.prepare(`
          SELECT * FROM dobras_cutaneas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        const medidas = db.prepare(`
          SELECT * FROM medidas_antropometricas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        return this.mapearAvaliacao(avaliacao, dobras, medidas);
      }));
    } catch (error) {
      console.error('Erro ao buscar avaliações físicas:', error);
      return Promise.resolve([]);
    }
  }

  private static mapearAvaliacao(
    avaliacao: any,
    dobras: any,
    medidas: any
  ): AvaliacaoFisica {
    const avaliacaoMapeada: AvaliacaoFisica = {
      id: avaliacao.id,
      cliente_id: avaliacao.cliente_id,
      data_avaliacao: avaliacao.data_avaliacao,
      peso: avaliacao.peso,
      altura: avaliacao.altura,
      idade: avaliacao.idade,
      nivelAtividade: avaliacao.nivel_atividade || 'Sedentário',
      intensidadeExercicios: avaliacao.intensidade_exercicios || 'Leve',
      frequenciaSemanal: avaliacao.frequencia_semanal || 0,
      tipoTrabalho: avaliacao.tipo_trabalho || 'Sedentário',
      objetivo: avaliacao.objetivo || 'manutenção',
      percentualGordura: avaliacao.percentual_gordura || 0,
      dobras_cutaneas: dobras ? {
        id: dobras.id,
        avaliacao_id: dobras.avaliacao_id,
        peitoral: dobras.peitoral,
        tricipital: dobras.tricipital,
        bicipital: dobras.bicipital,
        axilar_media: dobras.axilar_media,
        suprailiaca: dobras.suprailiaca,
        abdominal: dobras.abdominal,
        coxa: dobras.coxa,
        panturrilha: dobras.panturrilha,
        percentual_gordura: dobras.percentual_gordura
      } : {
        avaliacao_id: avaliacao.id,
        peitoral: 0,
        tricipital: 0,
        bicipital: 0,
        axilar_media: 0,
        suprailiaca: 0,
        abdominal: 0,
        coxa: 0,
        panturrilha: 0,
        percentual_gordura: 0
      },
      medidas_antropometricas: medidas ? {
        id: medidas.id,
        avaliacao_id: medidas.avaliacao_id,
        braco_direito: medidas.braco_direito,
        braco_esquerdo: medidas.braco_esquerdo,
        antebraco_direito: medidas.antebraco_direito,
        antebraco_esquerdo: medidas.antebraco_esquerdo,
        peitoral: medidas.peitoral,
        cintura: medidas.cintura,
        abdomen: medidas.abdomen,
        quadril: medidas.quadril,
        coxa_direita: medidas.coxa_direita,
        coxa_esquerda: medidas.coxa_esquerda,
        panturrilha_direita: medidas.panturrilha_direita,
        panturrilha_esquerda: medidas.panturrilha_esquerda
      } : {
        avaliacao_id: avaliacao.id,
        braco_direito: 0,
        braco_esquerdo: 0,
        antebraco_direito: 0,
        antebraco_esquerdo: 0,
        peitoral: 0,
        cintura: 0,
        abdomen: 0,
        quadril: 0,
        coxa_direita: 0,
        coxa_esquerda: 0,
        panturrilha_direita: 0,
        panturrilha_esquerda: 0
      }
    };

    return avaliacaoMapeada;
  }
} 