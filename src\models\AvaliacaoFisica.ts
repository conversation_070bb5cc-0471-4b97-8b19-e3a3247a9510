import { getDatabase } from '../db/db';

// Verificar se estamos no Electron de forma segura para tipagem
const isElectron = typeof window !== 'undefined' && 
                  window.navigator && 
                  /electron/i.test(window.navigator.userAgent);
const isDevelopment = process.env.NODE_ENV === 'development';

// Dados mock para desenvolvimento
const mockAvaliacoes: AvaliacaoFisica[] = [
  {
    id: 1,
    cliente_id: 1,
    data_avaliacao: '2023-06-01',
    peso: 75.5,
    altura: 175,
    idade: 30,
    nivelAtividade: 'Sedentário',
    intensidadeExercicios: 'Leve',
    frequenciaSemanal: 3,
    tipoTrabalho: 'Sedentário',
    objetivo: 'manutenção',
    percentualGordura: 15.5,
    dobras_cutaneas: {
      id: 1,
      avaliacao_id: 1,
      peitoral: 10,
      tricipital: 12,
      bicipital: 8,
      axilar_media: 9,
      suprailiaca: 15,
      abdominal: 18,
      coxa: 14,
      panturrilha: 10,
      percentual_gordura: 15.5
    },
    medidas_antropometricas: {
      id: 1,
      avaliacao_id: 1,
      braco_direito: 32,
      braco_esquerdo: 31.5,
      antebraco_direito: 28,
      antebraco_esquerdo: 27.5,
      peitoral: 95,
      cintura: 80,
      abdomen: 85,
      quadril: 100,
      coxa_direita: 55,
      coxa_esquerda: 54.5,
      panturrilha_direita: 38,
      panturrilha_esquerda: 37.5
    }
  },
  {
    id: 2,
    cliente_id: 2,
    data_avaliacao: '2023-06-15',
    peso: 68.2,
    altura: 165,
    idade: 28,
    nivelAtividade: 'Levemente Ativo',
    intensidadeExercicios: 'Moderada',
    frequenciaSemanal: 4,
    tipoTrabalho: 'Leve',
    objetivo: 'perda',
    percentualGordura: 12.8,
    dobras_cutaneas: {
      id: 2,
      avaliacao_id: 2,
      peitoral: 8,
      tricipital: 10,
      bicipital: 7,
      axilar_media: 8,
      suprailiaca: 12,
      abdominal: 15,
      coxa: 12,
      panturrilha: 8,
      percentual_gordura: 12.8
    },
    medidas_antropometricas: {
      id: 2,
      avaliacao_id: 2,
      braco_direito: 29,
      braco_esquerdo: 28.5,
      antebraco_direito: 25,
      antebraco_esquerdo: 24.5,
      peitoral: 90,
      cintura: 75,
      abdomen: 78,
      quadril: 95,
      coxa_direita: 52,
      coxa_esquerda: 51.5,
      panturrilha_direita: 36,
      panturrilha_esquerda: 35.5
    }
  }
];

export interface DobrasCutaneasBase {
  peitoral: number;
  tricipital: number;
  bicipital: number;
  axilar_media: number;
  suprailiaca: number;
  abdominal: number;
  coxa: number;
  panturrilha: number;
  percentual_gordura: number;
}

export interface DobrasCutaneas extends DobrasCutaneasBase {
  id?: number;
  avaliacao_id: number;
}

export interface MedidasAntropometricasBase {
  braco_direito: number;
  braco_esquerdo: number;
  antebraco_direito: number;
  antebraco_esquerdo: number;
  peitoral: number;
  cintura: number;
  abdomen: number;
  quadril: number;
  coxa_direita: number;
  coxa_esquerda: number;
  panturrilha_direita: number;
  panturrilha_esquerda: number;
}

export interface MedidasAntropometricas extends MedidasAntropometricasBase {
  id?: number;
  avaliacao_id: number;
}

export interface AvaliacaoFisica {
  id?: number;
  cliente_id: number;
  data_avaliacao: string;
  peso: number;
  altura: number;
  idade: number;
  dobras_cutaneas: DobrasCutaneas;
  medidas_antropometricas: MedidasAntropometricas;
  nivelAtividade: 'Sedentário' | 'Levemente Ativo' | 'Moderadamente Ativo' | 'Muito Ativo' | 'Extremamente Ativo';
  intensidadeExercicios: 'Leve' | 'Moderada' | 'Alta' | 'Muito Alta';
  frequenciaSemanal: number;
  tipoTrabalho: 'Sedentário' | 'Leve' | 'Moderado' | 'Intenso';
  objetivo: 'manutenção' | 'perda' | 'ganho';
  percentualGordura: number;
  data?: string;
  circunferencia_abdominal?: number;
  massa_muscular?: number;
}

export class AvaliacaoFisicaModel {
  static async obterPorId(id: number): Promise<AvaliacaoFisica | null> {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const avaliacao = mockAvaliacoes.find(a => a.id === id);
      return avaliacao || null;
    }
    
    const db = getDatabase();
    try {
      const avaliacao = db.prepare(`
        SELECT * FROM avaliacoes_fisicas WHERE id = ?
      `).get(id);

      if (!avaliacao) return null;

      const dobras = db.prepare(`
        SELECT * FROM dobras_cutaneas WHERE avaliacao_id = ?
      `).get(id);

      const medidas = db.prepare(`
        SELECT * FROM medidas_antropometricas WHERE avaliacao_id = ?
      `).get(id);

      return this.mapearAvaliacao(avaliacao, dobras, medidas);
    } catch (error) {
      console.error('Erro ao obter avaliação física:', error);
      throw error;
    }
  }

  static async obterTodas(): Promise<AvaliacaoFisica[]> {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return Promise.resolve([...mockAvaliacoes]);
    }
    
    try {
      const db = getDatabase();
      const avaliacoes = db.prepare(`
        SELECT * FROM avaliacoes_fisicas ORDER BY data_avaliacao DESC
      `).all();

      return Promise.all(avaliacoes.map(async (avaliacao: any) => {
        const dobras = db.prepare(`
          SELECT * FROM dobras_cutaneas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        const medidas = db.prepare(`
          SELECT * FROM medidas_antropometricas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        return this.mapearAvaliacao(avaliacao, dobras, medidas);
      }));
    } catch (error) {
      console.error('Erro ao obter avaliações físicas:', error);
      return Promise.resolve([]);
    }
  }

  static async criar(avaliacao: AvaliacaoFisica): Promise<number> {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const novoId = mockAvaliacoes.length > 0 ? Math.max(...mockAvaliacoes.map(a => a.id || 0)) + 1 : 1;
      const novaAvaliacao = {
        ...avaliacao,
        id: novoId
      };
      
      if (avaliacao.dobras_cutaneas) {
        novaAvaliacao.dobras_cutaneas = {
          ...avaliacao.dobras_cutaneas,
          id: novoId,
          avaliacao_id: novoId
        };
      }
      
      if (avaliacao.medidas_antropometricas) {
        novaAvaliacao.medidas_antropometricas = {
          ...avaliacao.medidas_antropometricas,
          id: novoId,
          avaliacao_id: novoId
        };
      }
      
      mockAvaliacoes.push(novaAvaliacao);
      return novoId;
    }
    
    const db = getDatabase();
    try {
      db.prepare('BEGIN').run();

      const { lastInsertRowid: avaliacaoId } = db.prepare(`
        INSERT INTO avaliacoes_fisicas (cliente_id, data_avaliacao, peso, altura, idade)
        VALUES (?, ?, ?, ?, ?)
      `).run(
        avaliacao.cliente_id,
        avaliacao.data_avaliacao,
        avaliacao.peso,
        avaliacao.altura,
        avaliacao.idade
      );

      if (avaliacao.dobras_cutaneas) {
        db.prepare(`
          INSERT INTO dobras_cutaneas (
            avaliacao_id, peitoral, tricipital, bicipital, axilar_media,
            suprailiaca, abdominal, coxa, panturrilha, percentual_gordura
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          avaliacaoId,
          avaliacao.dobras_cutaneas.peitoral,
          avaliacao.dobras_cutaneas.tricipital,
          avaliacao.dobras_cutaneas.bicipital,
          avaliacao.dobras_cutaneas.axilar_media,
          avaliacao.dobras_cutaneas.suprailiaca,
          avaliacao.dobras_cutaneas.abdominal,
          avaliacao.dobras_cutaneas.coxa,
          avaliacao.dobras_cutaneas.panturrilha,
          avaliacao.dobras_cutaneas.percentual_gordura
        );
      }

      if (avaliacao.medidas_antropometricas) {
        db.prepare(`
          INSERT INTO medidas_antropometricas (
            avaliacao_id, braco_direito, braco_esquerdo, antebraco_direito,
            antebraco_esquerdo, peitoral, cintura, abdomen, quadril,
            coxa_direita, coxa_esquerda, panturrilha_direita, panturrilha_esquerda
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          avaliacaoId,
          avaliacao.medidas_antropometricas.braco_direito,
          avaliacao.medidas_antropometricas.braco_esquerdo,
          avaliacao.medidas_antropometricas.antebraco_direito,
          avaliacao.medidas_antropometricas.antebraco_esquerdo,
          avaliacao.medidas_antropometricas.peitoral,
          avaliacao.medidas_antropometricas.cintura,
          avaliacao.medidas_antropometricas.abdomen,
          avaliacao.medidas_antropometricas.quadril,
          avaliacao.medidas_antropometricas.coxa_direita,
          avaliacao.medidas_antropometricas.coxa_esquerda,
          avaliacao.medidas_antropometricas.panturrilha_direita,
          avaliacao.medidas_antropometricas.panturrilha_esquerda
        );
      }

      db.prepare('COMMIT').run();
      return Number(avaliacaoId);
    } catch (error) {
      db.prepare('ROLLBACK').run();
      console.error('Erro ao criar avaliação física:', error);
      throw error;
    }
  }

  static async atualizar(id: number, avaliacao: Partial<AvaliacaoFisica>): Promise<boolean> {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockAvaliacoes.findIndex(a => a.id === id);
      if (index !== -1) {
        // Atualizar a avaliação principal
        mockAvaliacoes[index] = {
          ...mockAvaliacoes[index],
          ...avaliacao,
          id
        };
        
        // Atualizar dobras cutâneas se fornecidas
        if (avaliacao.dobras_cutaneas) {
          if (mockAvaliacoes[index].dobras_cutaneas) {
            mockAvaliacoes[index].dobras_cutaneas = {
              ...mockAvaliacoes[index].dobras_cutaneas,
              ...avaliacao.dobras_cutaneas,
              id: mockAvaliacoes[index].dobras_cutaneas?.id,
              avaliacao_id: id
            };
          } else {
            mockAvaliacoes[index].dobras_cutaneas = {
              ...avaliacao.dobras_cutaneas,
              id: Math.floor(Math.random() * 1000) + 1,
              avaliacao_id: id
            };
          }
        }
        
        // Atualizar medidas antropométricas se fornecidas
        if (avaliacao.medidas_antropometricas) {
          if (mockAvaliacoes[index].medidas_antropometricas) {
            mockAvaliacoes[index].medidas_antropometricas = {
              ...mockAvaliacoes[index].medidas_antropometricas,
              ...avaliacao.medidas_antropometricas,
              id: mockAvaliacoes[index].medidas_antropometricas?.id,
              avaliacao_id: id
            };
          } else {
            mockAvaliacoes[index].medidas_antropometricas = {
              ...avaliacao.medidas_antropometricas,
              id: Math.floor(Math.random() * 1000) + 1,
              avaliacao_id: id
            };
          }
        }
        
        return Promise.resolve(true);
      }
      return Promise.resolve(false);
    }
    
    try {
      const db = getDatabase();
      const campos = Object.keys(avaliacao).filter(campo => 
        campo !== 'id' && 
        campo !== 'dobras_cutaneas' && 
        campo !== 'medidas_antropometricas'
      );
      
      if (campos.length > 0) {
        const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
        const valores = campos.map(campo => (avaliacao as any)[campo]);
        
        const stmt = db.prepare(`UPDATE avaliacoes_fisicas SET ${setClauses} WHERE id = ?`);
        stmt.run([...valores, id]);
      }
      
      // Atualizar dobras cutâneas
      if (avaliacao.dobras_cutaneas) {
        const dobrasCutaneas = avaliacao.dobras_cutaneas;
        const dobraExistente = db.prepare('SELECT id FROM dobras_cutaneas WHERE avaliacao_id = ?').get(id);
        
        if (dobraExistente) {
          const camposDobras = Object.keys(dobrasCutaneas).filter(campo => campo !== 'id' && campo !== 'avaliacao_id');
          if (camposDobras.length > 0) {
            const setClausesDobras = camposDobras.map(campo => `${campo} = ?`).join(', ');
            const valoresDobras = camposDobras.map(campo => (dobrasCutaneas as any)[campo]);
            
            const stmtDobras = db.prepare(`UPDATE dobras_cutaneas SET ${setClausesDobras} WHERE avaliacao_id = ?`);
            stmtDobras.run([...valoresDobras, id]);
          }
        } else {
          const stmtDobras = db.prepare(`
            INSERT INTO dobras_cutaneas (
              avaliacao_id, peitoral, tricipital, bicipital, axilar_media, 
              suprailiaca, abdominal, coxa, panturrilha, percentual_gordura
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);
          
          stmtDobras.run(
            id,
            dobrasCutaneas.peitoral,
            dobrasCutaneas.tricipital,
            dobrasCutaneas.bicipital,
            dobrasCutaneas.axilar_media,
            dobrasCutaneas.suprailiaca,
            dobrasCutaneas.abdominal,
            dobrasCutaneas.coxa,
            dobrasCutaneas.panturrilha,
            dobrasCutaneas.percentual_gordura
          );
        }
      }
      
      // Atualizar medidas antropométricas
      if (avaliacao.medidas_antropometricas) {
        const medidasAntropometricas = avaliacao.medidas_antropometricas;
        const medidaExistente = db.prepare('SELECT id FROM medidas_antropometricas WHERE avaliacao_id = ?').get(id);
        
        if (medidaExistente) {
          const camposMedidas = Object.keys(medidasAntropometricas).filter(campo => campo !== 'id' && campo !== 'avaliacao_id');
          if (camposMedidas.length > 0) {
            const setClausesMedidas = camposMedidas.map(campo => `${campo} = ?`).join(', ');
            const valoresMedidas = camposMedidas.map(campo => (medidasAntropometricas as any)[campo]);
            
            const stmtMedidas = db.prepare(`UPDATE medidas_antropometricas SET ${setClausesMedidas} WHERE avaliacao_id = ?`);
            stmtMedidas.run([...valoresMedidas, id]);
          }
        } else {
          const stmtMedidas = db.prepare(`
            INSERT INTO medidas_antropometricas (
              avaliacao_id, braco_direito, braco_esquerdo, antebraco_direito, antebraco_esquerdo,
              peitoral, cintura, abdomen, quadril, coxa_direita, coxa_esquerda,
              panturrilha_direita, panturrilha_esquerda
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);
          
          stmtMedidas.run(
            id,
            medidasAntropometricas.braco_direito,
            medidasAntropometricas.braco_esquerdo,
            medidasAntropometricas.antebraco_direito,
            medidasAntropometricas.antebraco_esquerdo,
            medidasAntropometricas.peitoral,
            medidasAntropometricas.cintura,
            medidasAntropometricas.abdomen,
            medidasAntropometricas.quadril,
            medidasAntropometricas.coxa_direita,
            medidasAntropometricas.coxa_esquerda,
            medidasAntropometricas.panturrilha_direita,
            medidasAntropometricas.panturrilha_esquerda
          );
        }
      }
      
      return Promise.resolve(true);
    } catch (error) {
      console.error('Erro ao atualizar avaliação física:', error);
      return Promise.resolve(false);
    }
  }

  static async excluir(id: number): Promise<void> {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockAvaliacoes.findIndex(a => a.id === id);
      if (index !== -1) {
        mockAvaliacoes.splice(index, 1);
      }
      return;
    }
    
    const db = getDatabase();
    try {
      db.prepare('BEGIN').run();

      // Excluir registros relacionados
      db.prepare('DELETE FROM dobras_cutaneas WHERE avaliacao_id = ?').run(id);
      db.prepare('DELETE FROM medidas_antropometricas WHERE avaliacao_id = ?').run(id);
      db.prepare('DELETE FROM avaliacoes_fisicas WHERE id = ?').run(id);

      db.prepare('COMMIT').run();
    } catch (error) {
      db.prepare('ROLLBACK').run();
      console.error('Erro ao excluir avaliação física:', error);
      throw error;
    }
  }

  static async buscar(termo: string): Promise<AvaliacaoFisica[]> {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const termoLowerCase = termo.toLowerCase();
      const avaliacoesFiltradas = mockAvaliacoes.filter(avaliacao => {
        // Buscar pelo ID do cliente
        if (avaliacao.cliente_id.toString().includes(termoLowerCase)) {
          return true;
        }
        
        // Buscar pela data da avaliação
        if (avaliacao.data_avaliacao.toLowerCase().includes(termoLowerCase)) {
          return true;
        }
        
        return false;
      });
      
      return Promise.resolve(avaliacoesFiltradas);
    }
    
    try {
      const db = getDatabase();
      const avaliacoes = db.prepare(`
        SELECT af.* FROM avaliacoes_fisicas af
        JOIN clientes c ON af.cliente_id = c.id
        WHERE 
          c.nome LIKE ? OR
          af.data_avaliacao LIKE ? OR
          af.id LIKE ?
        ORDER BY af.data_avaliacao DESC
      `).all(
        `%${termo}%`,
        `%${termo}%`,
        `%${termo}%`
      );

      return Promise.all(avaliacoes.map(async (avaliacao: any) => {
        const dobras = db.prepare(`
          SELECT * FROM dobras_cutaneas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        const medidas = db.prepare(`
          SELECT * FROM medidas_antropometricas WHERE avaliacao_id = ?
        `).get(avaliacao.id);

        return this.mapearAvaliacao(avaliacao, dobras, medidas);
      }));
    } catch (error) {
      console.error('Erro ao buscar avaliações físicas:', error);
      return Promise.resolve([]);
    }
  }

  private static mapearAvaliacao(
    avaliacao: any,
    dobras: any,
    medidas: any
  ): AvaliacaoFisica {
    const avaliacaoMapeada: AvaliacaoFisica = {
      id: avaliacao.id,
      cliente_id: avaliacao.cliente_id,
      data_avaliacao: avaliacao.data_avaliacao,
      peso: avaliacao.peso,
      altura: avaliacao.altura,
      idade: avaliacao.idade,
      nivelAtividade: avaliacao.nivel_atividade || 'Sedentário',
      intensidadeExercicios: avaliacao.intensidade_exercicios || 'Leve',
      frequenciaSemanal: avaliacao.frequencia_semanal || 0,
      tipoTrabalho: avaliacao.tipo_trabalho || 'Sedentário',
      objetivo: avaliacao.objetivo || 'manutenção',
      percentualGordura: avaliacao.percentual_gordura || 0,
      dobras_cutaneas: dobras ? {
        id: dobras.id,
        avaliacao_id: dobras.avaliacao_id,
        peitoral: dobras.peitoral,
        tricipital: dobras.tricipital,
        bicipital: dobras.bicipital,
        axilar_media: dobras.axilar_media,
        suprailiaca: dobras.suprailiaca,
        abdominal: dobras.abdominal,
        coxa: dobras.coxa,
        panturrilha: dobras.panturrilha,
        percentual_gordura: dobras.percentual_gordura
      } : {
        avaliacao_id: avaliacao.id,
        peitoral: 0,
        tricipital: 0,
        bicipital: 0,
        axilar_media: 0,
        suprailiaca: 0,
        abdominal: 0,
        coxa: 0,
        panturrilha: 0,
        percentual_gordura: 0
      },
      medidas_antropometricas: medidas ? {
        id: medidas.id,
        avaliacao_id: medidas.avaliacao_id,
        braco_direito: medidas.braco_direito,
        braco_esquerdo: medidas.braco_esquerdo,
        antebraco_direito: medidas.antebraco_direito,
        antebraco_esquerdo: medidas.antebraco_esquerdo,
        peitoral: medidas.peitoral,
        cintura: medidas.cintura,
        abdomen: medidas.abdomen,
        quadril: medidas.quadril,
        coxa_direita: medidas.coxa_direita,
        coxa_esquerda: medidas.coxa_esquerda,
        panturrilha_direita: medidas.panturrilha_direita,
        panturrilha_esquerda: medidas.panturrilha_esquerda
      } : {
        avaliacao_id: avaliacao.id,
        braco_direito: 0,
        braco_esquerdo: 0,
        antebraco_direito: 0,
        antebraco_esquerdo: 0,
        peitoral: 0,
        cintura: 0,
        abdomen: 0,
        quadril: 0,
        coxa_direita: 0,
        coxa_esquerda: 0,
        panturrilha_direita: 0,
        panturrilha_esquerda: 0
      }
    };

    return avaliacaoMapeada;
  }
} 