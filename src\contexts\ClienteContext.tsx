import React, { createContext, useContext, useState, useCallback } from 'react';
import { Cliente, ClienteModel } from '../models/Cliente';

interface ClienteContextType {
  clientes: Cliente[];
  clienteSelecionado: Cliente | null;
  carregando: boolean;
  erro: string | null;
  carregarClientes: () => Promise<void>;
  criarCliente: (cliente: Cliente) => Promise<void>;
  atualizarCliente: (id: number, cliente: Cliente) => Promise<void>;
  excluirCliente: (id: number) => Promise<void>;
  buscarClientes: (termo: string) => Promise<void>;
  selecionarCliente: (cliente: Cliente | null) => void;
}

const ClienteContext = createContext<ClienteContextType | undefined>(undefined);

export const useCliente = () => {
  const context = useContext(ClienteContext);
  if (!context) {
    throw new Error('useCliente deve ser usado dentro de um ClienteProvider');
  }
  return context;
};

export const ClienteProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [clientes, setClientes] = useState<Cliente[]>([]);
  const [clienteSelecionado, setClienteSelecionado] = useState<Cliente | null>(null);
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const carregarClientes = useCallback(async () => {
    try {
      setCarregando(true);
      setErro(null);
      const clientesCarregados = await ClienteModel.obterTodos();
      setClientes(clientesCarregados);
    } catch (error) {
      setErro('Erro ao carregar clientes');
      console.error('Erro ao carregar clientes:', error);
    } finally {
      setCarregando(false);
    }
  }, []);

  const criarCliente = useCallback(async (cliente: Cliente) => {
    try {
      setCarregando(true);
      setErro(null);
      await ClienteModel.criar(cliente);
      await carregarClientes();
    } catch (error) {
      setErro('Erro ao criar cliente');
      console.error('Erro ao criar cliente:', error);
    } finally {
      setCarregando(false);
    }
  }, [carregarClientes]);

  const atualizarCliente = useCallback(async (id: number, cliente: Cliente) => {
    try {
      setCarregando(true);
      setErro(null);
      await ClienteModel.atualizar(id, cliente);
      await carregarClientes();
    } catch (error) {
      setErro('Erro ao atualizar cliente');
      console.error('Erro ao atualizar cliente:', error);
    } finally {
      setCarregando(false);
    }
  }, [carregarClientes]);

  const excluirCliente = useCallback(async (id: number) => {
    try {
      setCarregando(true);
      setErro(null);
      await ClienteModel.excluir(id);
      await carregarClientes();
      if (clienteSelecionado?.id === id) {
        setClienteSelecionado(null);
      }
    } catch (error) {
      setErro('Erro ao excluir cliente');
      console.error('Erro ao excluir cliente:', error);
    } finally {
      setCarregando(false);
    }
  }, [carregarClientes, clienteSelecionado]);

  const buscarClientes = useCallback(async (termo: string) => {
    try {
      setCarregando(true);
      setErro(null);
      const clientesEncontrados = await ClienteModel.buscar(termo);
      setClientes(clientesEncontrados);
    } catch (error) {
      setErro('Erro ao buscar clientes');
      console.error('Erro ao buscar clientes:', error);
    } finally {
      setCarregando(false);
    }
  }, []);

  const selecionarCliente = useCallback((cliente: Cliente | null) => {
    setClienteSelecionado(cliente);
  }, []);

  return (
    <ClienteContext.Provider
      value={{
        clientes,
        clienteSelecionado,
        carregando,
        erro,
        carregarClientes,
        criarCliente,
        atualizarCliente,
        excluirCliente,
        buscarClientes,
        selecionarCliente
      }}
    >
      {children}
    </ClienteContext.Provider>
  );
};

export default ClienteContext; 