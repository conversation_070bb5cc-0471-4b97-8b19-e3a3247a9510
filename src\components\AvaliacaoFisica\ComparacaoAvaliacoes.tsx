import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Stack,
  Divider,
  IconButton,
  Tooltip as MuiTooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent
} from '@mui/material';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import { AvaliacaoFisica } from '../../models/AvaliacaoFisica';
import { colors } from '../../styles/colors';
import { format } from 'date-fns';
import { useAvaliacaoFisica } from '../../contexts/AvaliacaoFisicaContext';

interface ComparacaoAvaliacoesProps {
  avaliacoes: AvaliacaoFisica[];
}

const ComparacaoAvaliacoes: React.FC<ComparacaoAvaliacoesProps> = ({ avaliacoes }) => {
  const [avaliacaoAnterior, setAvaliacaoAnterior] = useState<AvaliacaoFisica | null>(null);
  const [avaliacaoAtual, setAvaliacaoAtual] = useState<AvaliacaoFisica | null>(null);

  const handleChangeAvaliacaoAnterior = (e: SelectChangeEvent) => {
    const avaliacao = avaliacoes.find(a => a.id === Number(e.target.value));
    setAvaliacaoAnterior(avaliacao || null);
  };

  const handleChangeAvaliacaoAtual = (e: SelectChangeEvent) => {
    const avaliacao = avaliacoes.find(a => a.id === Number(e.target.value));
    setAvaliacaoAtual(avaliacao || null);
  };

  const calcularDiferenca = (valorAtual: number, valorAnterior: number): number => {
    return Number((valorAtual - valorAnterior).toFixed(2));
  };

  const calcularPercentual = (valorAtual: number, valorAnterior: number): number => {
    if (valorAnterior === 0) return 0;
    return Number(((valorAtual - valorAnterior) / valorAnterior * 100).toFixed(2));
  };

  const renderIndicadorEvolucao = (diferenca: number, positivo: boolean = true) => {
    if (diferenca > 0) {
      return <TrendingUpIcon sx={{ color: positivo ? colors.sea : '#FF4D4D' }} />;
    } else if (diferenca < 0) {
      return <TrendingDownIcon sx={{ color: positivo ? '#FF4D4D' : colors.sea }} />;
    }
    return <TrendingFlatIcon sx={{ color: colors.ocean }} />;
  };

  const renderComparacao = (
    titulo: string,
    valorAtual: number,
    valorAnterior: number,
    unidade: string = '',
    tooltipPositivo: string = 'Aumento',
    tooltipNegativo: string = 'Redução',
    positivo: boolean = true
  ) => {
    const diferenca = calcularDiferenca(valorAtual, valorAnterior);
    const percentual = calcularPercentual(valorAtual, valorAnterior);
    const tooltipTexto = diferenca > 0 ? tooltipPositivo : tooltipNegativo;

    return (
      <Grid item xs={12} sm={6} md={6}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: { xs: 2, sm: 3 }, 
            borderRadius: { xs: 3, md: 4 },
            border: `1px solid ${colors.sea}`,
            bgcolor: colors.cloud,
            height: '100%',
            '&:hover': {
              borderColor: colors.sea,
              boxShadow: `0px 8px 16px ${colors.sea}19`
            }
          }}
        >
          <Stack spacing={{ xs: 1.5, sm: 2 }}>
            <Typography 
              variant="h6" 
              fontWeight="bold" 
              sx={{ 
                color: colors.ocean,
                fontSize: { xs: '1rem', sm: '1.25rem' }
              }}
            >
              {titulo}
            </Typography>
            <Grid container spacing={{ xs: 1, sm: 2 }}>
              <Grid item xs={6}>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: `${colors.ocean}99`,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  Anterior
                </Typography>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    color: colors.ocean,
                    fontSize: { xs: '0.875rem', sm: '1.25rem' }
                  }}
                >
                  {valorAnterior}{unidade}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: `${colors.ocean}99`,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  Atual
                </Typography>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    color: colors.ocean,
                    fontSize: { xs: '0.875rem', sm: '1.25rem' }
                  }}
                >
                  {valorAtual}{unidade}
                </Typography>
              </Grid>
            </Grid>
            <Divider sx={{ borderColor: `${colors.ocean}1A` }} />
            <Stack direction="row" alignItems="center" spacing={1}>
              <MuiTooltip title={tooltipTexto}>
                {renderIndicadorEvolucao(diferenca, positivo)}
              </MuiTooltip>
              <Typography 
                sx={{ 
                  color: colors.ocean,
                  fontSize: { xs: '0.75rem', sm: '0.875rem' }
                }}
              >
                {Math.abs(diferenca)}{unidade} ({Math.abs(percentual).toFixed(1)}%)
              </Typography>
            </Stack>
          </Stack>
        </Paper>
      </Grid>
    );
  };

  return (
    <Box>
      <Paper 
        elevation={0} 
        sx={{ 
          p: { xs: 2, sm: 3 }, 
          mb: { xs: 2, sm: 3 }, 
          borderRadius: { xs: 3, md: 4 }, 
          background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
          color: colors.cloud
        }}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <CompareArrowsIcon fontSize="large" />
          <Box>
            <Typography 
              variant="h5" 
              fontWeight="bold" 
              gutterBottom
              sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }}
            >
              Comparação de Avaliações
            </Typography>
            <Typography 
              variant="subtitle1"
              sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
            >
              Compare os resultados entre duas avaliações físicas
            </Typography>
          </Box>
        </Stack>
      </Paper>

      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel id="avaliacao-anterior-label">Avaliação Anterior</InputLabel>
            <Select
              labelId="avaliacao-anterior-label"
              value={avaliacaoAnterior?.id?.toString() || ''}
              label="Avaliação Anterior"
              onChange={handleChangeAvaliacaoAnterior}
              sx={{
                borderRadius: 2,
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: colors.ocean,
                  },
                  '&:hover fieldset': {
                    borderColor: colors.sea,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  },
                },
              }}
            >
              {avaliacoes.map((avaliacao) => (
                <MenuItem key={avaliacao.id} value={avaliacao.id?.toString()}>
                  {format(new Date(avaliacao.data_avaliacao), 'dd/MM/yyyy')}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel id="avaliacao-atual-label">Avaliação Atual</InputLabel>
            <Select
              labelId="avaliacao-atual-label"
              value={avaliacaoAtual?.id?.toString() || ''}
              label="Avaliação Atual"
              onChange={handleChangeAvaliacaoAtual}
              sx={{
                borderRadius: 2,
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: colors.ocean,
                  },
                  '&:hover fieldset': {
                    borderColor: colors.sea,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  },
                },
              }}
            >
              {avaliacoes.map((avaliacao) => (
                <MenuItem key={avaliacao.id} value={avaliacao.id?.toString()}>
                  {format(new Date(avaliacao.data_avaliacao), 'dd/MM/yyyy')}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {avaliacaoAnterior && avaliacaoAtual && (
        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {renderComparacao(
            'Peso Corporal',
            avaliacaoAtual.peso,
            avaliacaoAnterior.peso,
            ' kg',
            'Ganho de peso',
            'Perda de peso',
            false
          )}

          {renderComparacao(
            'IMC',
            Number((avaliacaoAtual.peso / Math.pow(avaliacaoAtual.altura / 100, 2)).toFixed(2)),
            Number((avaliacaoAnterior.peso / Math.pow(avaliacaoAnterior.altura / 100, 2)).toFixed(2)),
            ' kg/m²',
            'Aumento do IMC',
            'Redução do IMC',
            false
          )}

          {avaliacaoAtual.dobras_cutaneas?.percentual_gordura && avaliacaoAnterior.dobras_cutaneas?.percentual_gordura && (
            renderComparacao(
              'Percentual de Gordura',
              avaliacaoAtual.dobras_cutaneas.percentual_gordura,
              avaliacaoAnterior.dobras_cutaneas.percentual_gordura,
              '%',
              'Aumento do percentual de gordura',
              'Redução do percentual de gordura',
              false
            )
          )}

          {/* Medidas Antropométricas */}
          {avaliacaoAtual.medidas_antropometricas && avaliacaoAnterior.medidas_antropometricas && (
            <>
              {renderComparacao(
                'Braço Direito',
                avaliacaoAtual.medidas_antropometricas.braco_direito || 0,
                avaliacaoAnterior.medidas_antropometricas.braco_direito || 0,
                ' cm',
                'Aumento da circunferência',
                'Redução da circunferência',
                true
              )}

              {renderComparacao(
                'Peitoral',
                avaliacaoAtual.medidas_antropometricas.peitoral || 0,
                avaliacaoAnterior.medidas_antropometricas.peitoral || 0,
                ' cm',
                'Aumento da circunferência',
                'Redução da circunferência',
                true
              )}

              {renderComparacao(
                'Cintura',
                avaliacaoAtual.medidas_antropometricas.cintura || 0,
                avaliacaoAnterior.medidas_antropometricas.cintura || 0,
                ' cm',
                'Aumento da circunferência',
                'Redução da circunferência',
                false
              )}

              {renderComparacao(
                'Quadril',
                avaliacaoAtual.medidas_antropometricas.quadril || 0,
                avaliacaoAnterior.medidas_antropometricas.quadril || 0,
                ' cm',
                'Aumento da circunferência',
                'Redução da circunferência',
                false
              )}

              {renderComparacao(
                'Coxa Direita',
                avaliacaoAtual.medidas_antropometricas.coxa_direita || 0,
                avaliacaoAnterior.medidas_antropometricas.coxa_direita || 0,
                ' cm',
                'Aumento da circunferência',
                'Redução da circunferência',
                true
              )}

              {renderComparacao(
                'Panturrilha Direita',
                avaliacaoAtual.medidas_antropometricas.panturrilha_direita || 0,
                avaliacaoAnterior.medidas_antropometricas.panturrilha_direita || 0,
                ' cm',
                'Aumento da circunferência',
                'Redução da circunferência',
                true
              )}
            </>
          )}
        </Grid>
      )}

      {(!avaliacaoAnterior || !avaliacaoAtual) && (
        <Paper 
          elevation={0} 
          sx={{ 
            p: { xs: 2, sm: 3 }, 
            borderRadius: { xs: 3, md: 4 },
            border: `1px solid ${colors.sea}`,
            bgcolor: colors.cloud,
            textAlign: 'center'
          }}
        >
          <Typography 
            variant="h6" 
            sx={{ 
              color: colors.ocean,
              fontSize: { xs: '1rem', sm: '1.25rem' }
            }}
          >
            Selecione duas avaliações para comparar
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default ComparacaoAvaliacoes; 