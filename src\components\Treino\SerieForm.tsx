import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Grid,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Paper,
  SelectChangeEvent
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { Serie } from '../../models/Treino';

interface SerieFormProps {
  serie?: Serie;
  exercicioId: number;
  semanaAtiva: number;
  onSave: (serie: Serie) => void;
  onCancel: () => void;
}

const SerieForm: React.FC<SerieFormProps> = ({
  serie,
  exercicioId,
  semanaAtiva,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<Partial<Serie>>({
    semana: semanaAtiva,
    numero_serie: 1,
    repeticoes: 12,
    carga: 10,
    volume_carga: 120
  });
  
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    if (serie) {
      setFormData({
        ...serie
      });
    } else {
      setFormData({
        semana: semanaAtiva,
        numero_serie: 1,
        repeticoes: 12,
        carga: 10,
        volume_carga: 120
      });
    }
  }, [serie, semanaAtiva]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | { name?: string; value: unknown }> | SelectChangeEvent
  ) => {
    const { name, value } = e.target;
    if (!name) return;
    
    let newValue = value;
    
    // Converte para número quando necessário
    if (name === 'repeticoes' || name === 'carga' || name === 'numero_serie') {
      newValue = Number(value);
    }
    
    setFormData(prev => {
      const newData = { ...prev, [name]: newValue };
      
      // Calcula o volume de carga automaticamente
      if (name === 'repeticoes' || name === 'carga') {
        const repeticoes = name === 'repeticoes' ? Number(value) : (prev.repeticoes || 0);
        const carga = name === 'carga' ? Number(value) : (prev.carga || 0);
        newData.volume_carga = repeticoes * carga;
      }
      
      return newData;
    });
    
    // Limpa o erro quando o campo é alterado
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.semana || formData.semana < 1 || formData.semana > 8) {
      newErrors.semana = 'A semana deve ser um número entre 1 e 8';
    }
    
    if (!formData.numero_serie || formData.numero_serie < 1) {
      newErrors.numero_serie = 'O número da série deve ser positivo';
    }
    
    if (!formData.repeticoes || formData.repeticoes < 1) {
      newErrors.repeticoes = 'O número de repetições deve ser positivo';
    }
    
    if (formData.carga === undefined || formData.carga < 0) {
      newErrors.carga = 'A carga deve ser um número não negativo';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    const serieData: Serie = {
      ...formData as Serie,
      exercicio_id: exercicioId,
      volume_carga: (formData.repeticoes || 0) * (formData.carga || 0)
    };
    
    onSave(serieData);
  };

  const semanas = [1, 2, 3, 4, 5, 6, 7, 8];

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {serie ? 'Editar Série' : 'Nova Série'}
      </Typography>
      
      <Divider sx={{ mb: 3 }} />
      
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.semana} required>
              <InputLabel id="semana-label">Semana</InputLabel>
              <Select
                labelId="semana-label"
                name="semana"
                value={formData.semana || ''}
                onChange={(e) => {
                  // Tratamento específico para o Select
                  const event = e as SelectChangeEvent;
                  handleChange(event);
                }}
                label="Semana"
              >
                {semanas.map((semana) => (
                  <MenuItem key={semana} value={semana}>
                    Semana {semana}
                  </MenuItem>
                ))}
              </Select>
              {errors.semana && <FormHelperText>{errors.semana}</FormHelperText>}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Número da Série"
              name="numero_serie"
              type="number"
              value={formData.numero_serie || ''}
              onChange={handleChange}
              error={!!errors.numero_serie}
              helperText={errors.numero_serie}
              required
              InputProps={{ inputProps: { min: 1 } }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Repetições"
              name="repeticoes"
              type="number"
              value={formData.repeticoes || ''}
              onChange={handleChange}
              error={!!errors.repeticoes}
              helperText={errors.repeticoes}
              required
              InputProps={{ inputProps: { min: 1 } }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Carga (kg)"
              name="carga"
              type="number"
              value={formData.carga || ''}
              onChange={handleChange}
              error={!!errors.carga}
              helperText={errors.carga}
              required
              InputProps={{ inputProps: { min: 0, step: 0.5 } }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Volume de Carga"
              value={formData.volume_carga || 0}
              InputProps={{ readOnly: true }}
              disabled
              helperText="Calculado automaticamente (Repetições × Carga)"
            />
          </Grid>
          
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={onCancel}
              startIcon={<CancelIcon />}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
            >
              {serie ? 'Atualizar' : 'Salvar'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};

export default SerieForm; 