import React from 'react';
import { Box, Container, Typography, Link, Stack, Divider } from '@mui/material';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import { colors } from '../../styles/colors';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <Box
      component="footer"
      sx={{
        py: 4,
        px: 2,
        mt: 5,
        backgroundColor: `${colors.sea}0A`,
        borderTop: `1px solid ${colors.sea}1A`,
        borderRadius: '16px 16px 0 0',
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box 
              sx={{ 
                width: 40,
                height: 40,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                mr: 2,
                background: colors.sea,
                boxShadow: `0px 4px 8px ${colors.sea}33`
              }}
            >
              <FitnessCenterIcon sx={{ color: colors.cloud, fontSize: '1.2rem' }} />
            </Box>
            <Typography 
              variant="h6" 
              sx={{ 
                fontWeight: 'bold',
                color: colors.ocean,
                fontSize: '1.1rem'
              }}
            >
              Hyper Personal
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={3} divider={<Divider orientation="vertical" flexItem sx={{ borderColor: `${colors.ocean}1A` }} />}>
            <Link href="#" underline="hover" sx={{ color: `${colors.ocean}B3`, fontWeight: 500, '&:hover': { color: colors.sea } }}>
              Termos de Uso
            </Link>
            <Link href="#" underline="hover" sx={{ color: `${colors.ocean}B3`, fontWeight: 500, '&:hover': { color: colors.sea } }}>
              Privacidade
            </Link>
            <Link href="#" underline="hover" sx={{ color: `${colors.ocean}B3`, fontWeight: 500, '&:hover': { color: colors.sea } }}>
              Suporte
            </Link>
          </Stack>
        </Box>
        
        <Divider sx={{ my: 3, borderColor: `${colors.ocean}1A` }} />
        
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
            © {currentYear} Hyper Personal Trainer. Todos os direitos reservados.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer; 