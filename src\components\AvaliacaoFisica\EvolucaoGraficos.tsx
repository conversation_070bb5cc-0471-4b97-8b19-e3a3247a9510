import React from 'react';
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  Box,
  Typography,
  Grid,
  Stack,
  IconButton,
  Tooltip as MuiTooltip
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { AvaliacaoFisica } from '../../models/AvaliacaoFisica';
import { colors } from '../../styles/colors';
import { format } from 'date-fns';
import { Container } from '../common/Container';

interface EvolucaoGraficosProps {
  avaliacoes: AvaliacaoFisica[];
}

interface DadosGrafico {
  data: string;
  peso: number;
  percentual_gordura: number;
  massa_muscular: number;
  circunferencia_abdominal: number;
}

const calcularMassaMuscular = (peso: number, percentualGordura: number): number => {
  const massaGorda = (peso * percentualGordura) / 100;
  const massaMuscular = peso - massaGorda;
  return Number(massaMuscular.toFixed(2));
};

export const EvolucaoGraficos = ({ avaliacoes }: EvolucaoGraficosProps) => {
  const dados = avaliacoes
    .sort((a, b) => new Date(a.data_avaliacao).getTime() - new Date(b.data_avaliacao).getTime())
    .map(avaliacao => ({
      data: format(new Date(avaliacao.data_avaliacao), 'dd/MM/yyyy'),
      peso: avaliacao.peso,
      percentual_gordura: avaliacao.dobras_cutaneas?.percentual_gordura || 0,
      massa_muscular: calcularMassaMuscular(avaliacao.peso, avaliacao.dobras_cutaneas?.percentual_gordura || 0),
      circunferencia_abdominal: avaliacao.medidas_antropometricas?.abdomen || 0
    }));

  const renderGrafico = (
    titulo: string,
    dataKey: keyof DadosGrafico,
    cor: string,
    unidade: string,
    descricao: string
  ) => {
    return (
      <Box sx={{ width: '100%', height: 300, mb: 3 }}>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
          <Typography variant="h6" sx={{ color: colors.ocean }}>
            {titulo}
          </Typography>
          <MuiTooltip title={descricao}>
            <HelpOutlineIcon sx={{ color: colors.ocean, fontSize: 20 }} />
          </MuiTooltip>
        </Stack>
        <ResponsiveContainer>
          <LineChart
            data={dados}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={`${colors.ocean}1A`} />
            <XAxis
              dataKey="data"
              height={60}
              interval={0}
              tick={{
                fontSize: 12,
                fill: colors.ocean
              }}
              stroke={colors.ocean}
              tickMargin={10}
            />
            <YAxis
              tick={{
                fontSize: 12,
                fill: colors.ocean
              }}
              stroke={colors.ocean}
              label={{
                value: unidade,
                angle: -90,
                position: 'insideLeft',
                style: {
                  textAnchor: 'middle',
                  fill: colors.ocean,
                  fontSize: 12
                }
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: colors.cloud,
                border: `1px solid ${colors.sea}`,
                borderRadius: '8px',
                color: colors.ocean,
                fontSize: 12
              }}
              formatter={(value: any) => [`${value}${unidade}`, titulo]}
            />
            <Legend
              wrapperStyle={{
                color: colors.ocean,
                fontSize: 12
              }}
            />
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={cor}
              strokeWidth={2}
              dot={{
                stroke: cor,
                strokeWidth: 2,
                r: 4,
                fill: colors.cloud
              }}
              activeDot={{
                stroke: cor,
                strokeWidth: 2,
                r: 6,
                fill: cor
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    );
  };

  return (
    <Container
      title="Evolução do Cliente"
      subtitle="Acompanhe o progresso através dos gráficos de evolução"
      useGradient
    >
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          {renderGrafico(
            'Evolução do Peso',
            'peso',
            colors.primary,
            'kg',
            'Acompanhamento da variação do peso corporal ao longo do tempo.'
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          {renderGrafico(
            'Evolução do Percentual de Gordura',
            'percentual_gordura',
            colors.secondary,
            '%',
            'Acompanhamento da variação do percentual de gordura corporal ao longo do tempo.'
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          {renderGrafico(
            'Evolução da Massa Muscular',
            'massa_muscular',
            colors.success,
            'kg',
            'Acompanhamento da variação da massa muscular estimada ao longo do tempo.'
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          {renderGrafico(
            'Evolução da Circunferência Abdominal',
            'circunferencia_abdominal',
            colors.warning,
            'cm',
            'Acompanhamento da variação da circunferência abdominal ao longo do tempo.'
          )}
        </Grid>
      </Grid>
    </Container>
  );
};

export default EvolucaoGraficos; 