import React from 'react';
import { Container } from '../common/Container';
import { AvaliacaoFisica } from '../../models/AvaliacaoFisica';

interface AvaliacaoFormProps {
  onSubmit: (avaliacao: AvaliacaoFisica) => void;
  initialData?: AvaliacaoFisica;
}

export const AvaliacaoForm = ({ onSubmit, initialData }: AvaliacaoFormProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Implementação do handleSubmit
  };

  return (
    <Container
      title={initialData ? "Editar Avaliação Física" : "Nova Avaliação Física"}
      subtitle="Preencha os dados da avaliação física do cliente"
      useGradient
    >
      <form onSubmit={handleSubmit}>
        {/* Implementação do formulário */}
      </form>
    </Container>
  );
}; 