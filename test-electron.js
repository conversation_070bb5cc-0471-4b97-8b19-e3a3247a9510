// Script de teste para verificar se o Electron funciona independentemente
const { app, BrowserWindow } = require('electron');

let mainWindow;

function createWindow() {
  console.log('🧪 TESTE: Criando janela do Electron...');
  
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // Carregar diretamente o localhost:3000 (assumindo que React está rodando)
  const testUrl = 'http://localhost:3000';
  console.log('🧪 TESTE: Carregando URL:', testUrl);
  
  mainWindow.loadURL(testUrl);
  
  mainWindow.webContents.openDevTools();
  
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
  
  // Log quando a página carregar
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ TESTE: Página carregada com sucesso!');
  });
  
  // Log se houver erro
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.log('❌ TESTE: Erro ao carregar página:', errorCode, errorDescription);
  });
}

app.on('ready', () => {
  console.log('🧪 TESTE: Electron está pronto!');
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

console.log('🧪 TESTE: Script iniciado. Aguardando Electron...');
