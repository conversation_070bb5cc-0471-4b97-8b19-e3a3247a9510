import db from '../database/db';

// Verificar se estamos no Electron de forma segura para tipagem
const isElectron = typeof window !== 'undefined' && 
                  window.navigator && 
                  /electron/i.test(window.navigator.userAgent);
const isDevelopment = process.env.NODE_ENV === 'development';

// Dados mock para desenvolvimento
const mockClientes: Cliente[] = [
  {
    id: 1,
    nome: '<PERSON>',
    data_nascimento: '1990-05-15',
    sexo: 'M',
    email: '<EMAIL>',
    telefone: '(11) 98765-4321',
    data_cadastro: '2023-01-10',
    observacoes: 'Cliente desde janeiro de 2023'
  },
  {
    id: 2,
    nome: '<PERSON>',
    data_nascimento: '1985-08-22',
    sexo: 'F',
    email: '<EMAIL>',
    telefone: '(11) 91234-5678',
    data_cadastro: '2023-02-05',
    observacoes: 'Foco em perda de peso'
  },
  {
    id: 3,
    nome: '<PERSON>',
    data_nascimento: '1995-03-10',
    sexo: 'M',
    email: '<EMAIL>',
    telefone: '(11) 99876-5432',
    data_cadastro: '2023-03-15',
    observacoes: 'Foco em hipertrofia'
  }
];

export interface Cliente {
  id?: number;
  nome: string;
  data_nascimento: string;
  sexo: 'M' | 'F';
  email?: string;
  telefone?: string;
  data_cadastro: string;
  observacoes?: string;
}

export const ClienteModel = {
  criar: (cliente: Cliente): number => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const novoId = mockClientes.length > 0 ? Math.max(...mockClientes.map(c => c.id || 0)) + 1 : 1;
      mockClientes.push({
        ...cliente,
        id: novoId,
        data_cadastro: cliente.data_cadastro || new Date().toISOString().split('T')[0]
      });
      return novoId;
    }
    
    const stmt = db.prepare(`
      INSERT INTO clientes (nome, data_nascimento, sexo, email, telefone, data_cadastro, observacoes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const info = stmt.run(
      cliente.nome,
      cliente.data_nascimento,
      cliente.sexo,
      cliente.email || null,
      cliente.telefone || null,
      cliente.data_cadastro || new Date().toISOString().split('T')[0],
      cliente.observacoes || null
    );
    
    return info.lastInsertRowid as number;
  },
  
  obterTodos: (): Cliente[] => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return [...mockClientes];
    }
    
    const stmt = db.prepare('SELECT * FROM clientes ORDER BY nome');
    return stmt.all();
  },
  
  obterPorId: (id: number): Cliente | undefined => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      return mockClientes.find(c => c.id === id);
    }
    
    const stmt = db.prepare('SELECT * FROM clientes WHERE id = ?');
    return stmt.get(id);
  },
  
  atualizar: (id: number, cliente: Partial<Cliente>): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockClientes.findIndex(c => c.id === id);
      if (index !== -1) {
        mockClientes[index] = { ...mockClientes[index], ...cliente, id };
        return true;
      }
      return false;
    }
    
    const campos = Object.keys(cliente).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;
    
    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (cliente as any)[campo]);
    
    const stmt = db.prepare(`UPDATE clientes SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);
    
    return info.changes > 0;
  },
  
  excluir: (id: number): boolean => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      const index = mockClientes.findIndex(c => c.id === id);
      if (index !== -1) {
        mockClientes.splice(index, 1);
        return true;
      }
      return false;
    }
    
    const stmt = db.prepare('DELETE FROM clientes WHERE id = ?');
    const info = stmt.run(id);
    
    return info.changes > 0;
  },
  
  buscar: (termo: string): Cliente[] => {
    // Se estiver em desenvolvimento no navegador, use dados mock
    if (isDevelopment && !isElectron) {
      if (!termo) return [...mockClientes];
      
      const termoLowerCase = termo.toLowerCase();
      return mockClientes.filter(c => 
        c.nome.toLowerCase().includes(termoLowerCase) || 
        (c.email && c.email.toLowerCase().includes(termoLowerCase)) ||
        (c.telefone && c.telefone.includes(termo))
      );
    }
    
    const stmt = db.prepare(`
      SELECT * FROM clientes 
      WHERE nome LIKE ? OR email LIKE ? OR telefone LIKE ?
      ORDER BY nome
    `);
    
    return stmt.all(`%${termo}%`, `%${termo}%`, `%${termo}%`);
  }
};

export default ClienteModel; 