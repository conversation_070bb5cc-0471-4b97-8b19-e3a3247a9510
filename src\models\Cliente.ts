import db from '../database/db';

export interface Cliente {
  id?: number;
  nome: string;
  data_nascimento: string;
  sexo: 'M' | 'F';
  email?: string;
  telefone?: string;
  data_cadastro: string;
  observacoes?: string;
}

export const ClienteModel = {
  criar: (cliente: Cliente): number => {
    const stmt = db.prepare(`
      INSERT INTO clientes (nome, data_nascimento, sexo, email, telefone, data_cadastro, observacoes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const info = stmt.run(
      cliente.nome,
      cliente.data_nascimento,
      cliente.sexo,
      cliente.email || null,
      cliente.telefone || null,
      cliente.data_cadastro || new Date().toISOString().split('T')[0],
      cliente.observacoes || null
    );

    return info.lastInsertRowid as number;
  },
  
  obterTodos: (): Cliente[] => {
    const stmt = db.prepare('SELECT * FROM clientes ORDER BY nome');
    return stmt.all();
  },
  
  obterPorId: (id: number): Cliente | undefined => {
    const stmt = db.prepare('SELECT * FROM clientes WHERE id = ?');
    return stmt.get(id);
  },
  
  atualizar: (id: number, cliente: Partial<Cliente>): boolean => {
    const campos = Object.keys(cliente).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;

    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (cliente as any)[campo]);

    const stmt = db.prepare(`UPDATE clientes SET ${setClauses} WHERE id = ?`);
    const info = stmt.run(...valores, id);

    return info.changes > 0;
  },
  
  excluir: (id: number): boolean => {
    const stmt = db.prepare('DELETE FROM clientes WHERE id = ?');
    const info = stmt.run(id);

    return info.changes > 0;
  },
  
  buscar: (termo: string): Cliente[] => {
    const stmt = db.prepare(`
      SELECT * FROM clientes
      WHERE nome LIKE ? OR email LIKE ? OR telefone LIKE ?
      ORDER BY nome
    `);

    return stmt.all(`%${termo}%`, `%${termo}%`, `%${termo}%`);
  }
};

export default ClienteModel; 